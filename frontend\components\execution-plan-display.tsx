'use client'

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Loader2, Edit3, Plus, Trash2 } from 'lucide-react'
import { useState } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils'

interface PlanStep {
  step: number
  action: 'call_mcp' | 'direct_response' | 'summary'
  server?: string
  description: string
  status?: 'pending' | 'executing' | 'completed' | 'skipped'
}

interface ExecutionPlan {
  use_mcp: 'yes' | 'no'
  reason: string
  plan: PlanStep[]
}

interface ExecutionPlanDisplayProps {
  plan: ExecutionPlan
  className?: string
  currentExecutingStep?: number
  isEditable?: boolean
  onPlanChange?: (updatedPlan: ExecutionPlan) => void
  onConfirmExecution?: (plan: ExecutionPlan) => void
}



const getActionText = (action: string) => {
  switch (action) {
    case 'call_mcp':
      return 'MCP调用'
    case 'summary':
      return '总结'
    case 'direct_response':
      return '直接回答'
    default:
      return action
  }
}



export default function ExecutionPlanDisplay({
  plan,
  className = '',
  currentExecutingStep,
  isEditable = false,
  onPlanChange,
  onConfirmExecution
}: ExecutionPlanDisplayProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editingPlan, setEditingPlan] = useState<ExecutionPlan>(plan)


  // 编辑功能函数
  const handleStartEdit = () => {
    setIsEditing(true)
    setEditingPlan({ ...plan })
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditingPlan(plan)
  }

  const handleSaveEdit = () => {
    setIsEditing(false)
    if (onPlanChange) {
      onPlanChange(editingPlan)
    }
  }

  const handleConfirmExecution = () => {
    if (onConfirmExecution) {
      onConfirmExecution(isEditing ? editingPlan : plan)
    }
  }

  const handleStepDescriptionChange = (stepNumber: number, newDescription: string) => {
    setEditingPlan(prev => ({
      ...prev,
      plan: prev.plan.map(step =>
        step.step === stepNumber
          ? { ...step, description: newDescription }
          : step
      )
    }))
  }

  const handleAddStep = () => {
    const newStepNumber = Math.max(...editingPlan.plan.map(s => s.step)) + 1
    const newStep: PlanStep = {
      step: newStepNumber,
      action: 'direct_response',
      description: '新任务步骤',
      status: 'pending'
    }
    setEditingPlan(prev => ({
      ...prev,
      plan: [...prev.plan, newStep]
    }))
  }

  const handleDeleteStep = (stepNumber: number) => {
    setEditingPlan(prev => ({
      ...prev,
      plan: prev.plan.filter(step => step.step !== stepNumber)
        .map((step, index) => ({ ...step, step: index + 1 })) // 重新编号
    }))
  }

  const handleActionChange = (stepNumber: number, newAction: 'call_mcp' | 'direct_response' | 'summary') => {
    setEditingPlan(prev => ({
      ...prev,
      plan: prev.plan.map(step =>
        step.step === stepNumber
          ? { ...step, action: newAction }
          : step
      )
    }))
  }

  // 根据当前执行步骤计算每个步骤的状态
  const getStepStatus = (stepNumber: number): string => {
    if (!currentExecutingStep) {
      // 如果没有指定当前执行步骤，所有步骤都显示为待执行
      return 'pending'
    }
    
    if (stepNumber < currentExecutingStep) return 'completed'
    if (stepNumber === currentExecutingStep) return 'executing'
    return 'pending'
  }

  // 计算进度
  const totalSteps = plan.plan.length
  const completedSteps = plan.plan.filter(step => {
    const status = step.status || getStepStatus(step.step)
    return status === 'completed'
  }).length
  // Removed unused currentStep variable
  


  return (
    <Card className={cn("shadow-sm border-gray-100/80", className)}>
      {/* 紧凑标题栏 */}
      <div className="px-5">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2.5 mb-2">
              <div className="w-5 h-5 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <Brain className="w-3 h-3 text-white" />
              </div>
              <h2 className="text-lg font-medium text-gray-900 tracking-tight">
                {isEditing ? 'Edit Plan' : 'Execution Plan'}
              </h2>
            </div>
            <p className="text-gray-600 text-sm leading-relaxed max-w-2xl break-words overflow-hidden">
              {plan.reason}
            </p>
          </div>

          <div className="flex items-center gap-2 ml-4">
            {/* 进度指示器 */}
            {totalSteps > 0 && !isEditing && (
              <div className="flex items-center gap-1.5 px-2.5 py-1 bg-gray-50 rounded-full">
                <div className="w-1.5 h-1.5 rounded-full bg-blue-500"></div>
                <span className="text-xs font-medium text-gray-700 tabular-nums">
                  {completedSteps}/{totalSteps}
                </span>
              </div>
            )}

            {/* 编辑按钮 */}
            {isEditable && !isEditing && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleStartEdit}
                className="h-8 px-3 text-xs font-medium text-gray-700 hover:text-gray-900 rounded-full cursor-pointer"
              >
                <Edit3 className="w-3.5 h-3.5 mr-1.5" />
                编辑
              </Button>
            )}

            {isEditing && (
              <div className="flex items-center gap-1.5">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCancelEdit}
                  className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-800 rounded-full cursor-pointer"
                >
                  取消
                </Button>
                <Button
                  size="sm"
                  onClick={handleSaveEdit}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#4d4d4d'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#000000'
                  }}
                  className="h-8 px-3 text-xs font-medium bg-black text-white rounded-full cursor-pointer transition-colors"
                  style={{ backgroundColor: '#000000' }}
                >
                  保存
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>



      {/* 紧凑任务步骤 */}
      <div className="px-5">
        {(isEditing ? editingPlan.plan : plan.plan) && (isEditing ? editingPlan.plan : plan.plan).length > 0 && (
          <div className="space-y-2.5">
            {(isEditing ? editingPlan.plan : plan.plan).map((step, index) => {
              const stepStatus = step.status || getStepStatus(step.step)

              return (
                <div key={index} className="group cursor-pointer">
                  <div className={cn(
                    "relative overflow-hidden rounded-lg border transition-all duration-200",
                    stepStatus === 'completed'
                      ? 'bg-gray-50/50 border-gray-200/50'
                      : stepStatus === 'executing'
                      ? 'bg-blue-50/30 border-blue-200/50 shadow-sm'
                      : isEditing
                      ? 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm cursor-pointer'
                      : 'bg-white border-gray-100 hover:border-gray-200 hover:shadow-sm'
                  )}>

                    <div className="pl-4 pr-4 py-3">
                      <div className="flex items-start gap-3">
                        {/* 步骤编号/状态 */}
                        <div className="flex-shrink-0 mt-0.5">
                          <div className={cn(
                            "w-5 h-5 rounded-full flex items-center justify-center text-xs font-medium transition-all",
                            stepStatus === 'completed'
                              ? 'bg-green-100 text-green-700'
                              : stepStatus === 'executing'
                              ? 'bg-blue-100 text-blue-700'
                              : 'bg-gray-100 text-gray-600'
                          )}>
                            {stepStatus === 'completed' ? (
                              <CheckCircle className="w-3 h-3" />
                            ) : stepStatus === 'executing' ? (
                              <Loader2 className="w-3 h-3 animate-spin" />
                            ) : (
                              step.step
                            )}
                          </div>
                        </div>

                        {/* 内容区域 */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1.5">
                            <div className="flex items-center gap-2">
                              {isEditing ? (
                                <Select
                                  value={step.action}
                                  onValueChange={(value) => handleActionChange(step.step, value as 'call_mcp' | 'direct_response' | 'summary')}
                                >
                                  <SelectTrigger className="h-7 w-32 text-xs font-medium cursor-pointer">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="call_mcp" className="cursor-pointer">MCP Call</SelectItem>
                                    <SelectItem value="direct_response" className="cursor-pointer">Direct Response</SelectItem>
                                    <SelectItem value="summary" className="cursor-pointer">Summary</SelectItem>
                                  </SelectContent>
                                </Select>
                              ) : (
                                <>
                                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    {getActionText(step.action)}
                                  </span>
                                  {step.server && (
                                    <span className="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 rounded-full font-medium">
                                      {step.server}
                                    </span>
                                  )}
                                </>
                              )}
                            </div>

                            {/* 编辑删除按钮 */}
                            {isEditing && editingPlan.plan.length > 1 && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteStep(step.step)}
                                className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0 text-gray-400 hover:text-red-500 hover:bg-red-50 cursor-pointer"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            )}
                          </div>

                          {/* 步骤描述 */}
                          {isEditing ? (
                            <Textarea
                              value={step.description}
                              onChange={(e) => handleStepDescriptionChange(step.step, e.target.value)}
                              className="text-xs resize-none cursor-text min-h-[1.5rem] leading-relaxed"
                              placeholder="Enter step description..."
                              style={{
                                height: 'auto',
                                minHeight: '1.5rem'
                              }}
                              onInput={(e) => {
                                const target = e.target as HTMLTextAreaElement;
                                target.style.height = 'auto';
                                target.style.height = target.scrollHeight + 'px';
                              }}
                            />
                          ) : (
                            <p className="text-xs text-gray-700 leading-relaxed">{step.description}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}

            {/* 添加步骤按钮 */}
            {isEditing && (
              <Button
                variant="outline"
                onClick={handleAddStep}
                className="w-full h-10 border-dashed border-gray-200 text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50/50 cursor-pointer"
              >
                <Plus className="w-3.5 h-3.5 mr-1.5" />
                <span className="text-xs font-medium">添加步骤</span>
              </Button>
            )}
          </div>
        )}
      </div>

      {/* 执行按钮 */}
      {isEditable && !isEditing && onConfirmExecution &&
       plan.plan.every(step => step.status === 'pending') && (
        <div className="px-5 pb-5 flex justify-center">
          <Button
            onClick={handleConfirmExecution}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#4d4d4d'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#000000'
            }}
            className="h-9 px-6 bg-black text-white font-medium shadow-sm hover:shadow-md cursor-pointer transition-colors rounded-full"
            style={{ backgroundColor: '#000000' }}
          >
            <Play className="w-3.5 h-3.5" />
              分步执行计划
          </Button>
        </div>
      )}
    </Card>
  )
} 