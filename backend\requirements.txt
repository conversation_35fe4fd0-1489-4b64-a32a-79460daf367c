# requirements.txt  ── Python 3.11+

# —— AutoGen 栈 ──────────────────────────────────────────
autogen==0.9.1.post0
autogen_agentchat==0.6.1
autogen-ext[mcp]==0.6.1
autogen-ext[anthropic]
autogen-ext[openai]

# —— Web API（可选；如只跑 CLI 可删掉） ———————————
fastapi>=0.114,<0.117            # 0.115.1 OK，未来升级也能兼容
uvicorn[standard]>=0.32,<0.33
uv
aiohttp

# —— 常用工具库 ——————————————————————————
python-dotenv>=1.0
python-multipart>=0.0.6
websockets>=11.0.3
PyYAML>=6.0

# —————————————————————————— 模型库 ——————————————————————————
openai
anthropic
mem0ai
azure-ai-inference

# —————————————————————————— Sandbox ——————————————————————————
e2b-code-interpreter
