import asyncio
from datetime import timedelta
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.tools.mcp import StreamableHttpMcpToolAdapter, StreamableHttpServerParams
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.ui import Console
from autogen_core import CancellationToken
from dotenv import load_dotenv, find_dotenv

env_path = find_dotenv(".env.local", raise_error_if_not_found=True)
load_dotenv(env_path, override=True)

async def main(tool_name) -> None:
    # Create server params for the remote MCP service
    server_params = StreamableHttpServerParams(
        url="https://router.mcp.so/mcp/duk82om9ag4o38",
        # headers={"Authorization": "Bearer your-api-key", "Content-Type": "application/json"},
        timeout=timedelta(seconds=30),
        sse_read_timeout=timedelta(seconds=60 * 5),
        terminate_on_close=True,
    )

    # Get the translation tool from the server
    adapter = await StreamableHttpMcpToolAdapter.from_server_params(server_params, tool_name)

    # Create an agent that can use the translation tool
    model_client = OpenAIChatCompletionClient(model="gpt-4o-mini")
    agent = AssistantAgent(
        name="assistant",
        model_client=model_client,
        tools=[adapter],
        system_message=f"You are a helpful assistant that can use the {tool_name} MCP server tool to help the user.",
    )

    await Console(
        agent.run_stream(task="规划一下路线，从北京到青岛", cancellation_token=CancellationToken())
    )


if __name__ == "__main__":
    asyncio.run(main("maps_distance"))
