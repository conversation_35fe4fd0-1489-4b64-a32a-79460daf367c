'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Upload, Play, Download, AlertCircle, Loader2, Code } from 'lucide-react'
import toast from 'react-hot-toast'
import Sidebar from '@/components/Sidebar'
import Image from 'next/image'

interface AnalysisResult {
  success: boolean
  output: string[]
  images: Array<{
    index: number
    data: string
    filename: string
  }>
  error?: {
    name: string
    value: string
    traceback: string
  }
  generated_code?: string
  dataset_path?: string
}

const CodeCell = ({ code, index = 1 }: { code: string, index?: number }) => {
  return (
    <div className="mb-3">
      <div className="flex">
        <span className="text-sm font-mono mr-2 mt-1" style={{ color: '#2f7fc1' }}>
          [{index}]
        </span>
        <pre className="flex-1 p-3 rounded text-sm overflow-x-auto font-mono text-gray-800 border" style={{ backgroundColor: '#f8f8f8' }}>
          <code>{code}</code>
        </pre>
      </div>
    </div>
  )
}

const OutputCell = ({ content }: { content: string }) => (
  <div className="mb-3">
    <pre className="bg-white border rounded p-3 text-sm overflow-x-auto font-mono text-gray-800">
      {content}
    </pre>
  </div>
)

const ImageCell = ({ image }: { image: { data: string, filename: string } }) => {
  const downloadImage = () => {
    try {
      const link = document.createElement('a')
      link.href = `data:image/png;base64,${image.data}`
      link.download = image.filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      toast.success(`已下载: ${image.filename}`)
    } catch {
      toast.error('下载失败')
    }
  }

  return (
    <div className="mb-3">
      <div className="flex items-center justify-between mb-3">
        <span className="text-sm text-gray-600">{image.filename}</span>
        <Button
          size="sm"
          variant="outline"
          onClick={downloadImage}
          className="h-6 px-2 text-xs"
        >
          <Download className="w-3 h-3 mr-1" />
          下载
        </Button>
      </div>
      <div className="bg-white border rounded p-2">
        <Image
          src={`data:image/png;base64,${image.data}`}
          alt={image.filename}
          width={800}
          height={600}
          className="w-full h-auto rounded"
          unoptimized
        />
      </div>
    </div>
  )
}

const ErrorCell = ({ error }: { error: { name: string, value: string, traceback: string } }) => (
  <div className="mb-3">
    <div className="flex items-center gap-2 mb-2">
      <AlertCircle className="w-4 h-4 text-red-600" />
      <span className="text-sm font-mono text-red-600">Error:</span>
    </div>
    <div className="space-y-2">
      <div className="bg-red-100 border border-red-300 rounded p-2">
        <span className="font-mono text-red-800 font-semibold">{error.name}:</span>
        <span className="font-mono text-red-700 ml-2">{error.value}</span>
      </div>
      {error.traceback && (
        <pre className="bg-red-900 text-red-100 p-3 rounded text-xs overflow-x-auto font-mono">
          {error.traceback}
        </pre>
      )}
    </div>
  </div>
)

export default function TestSandboxPage() {
  const [file, setFile] = useState<File | null>(null)
  const [analysisRequest, setAnalysisRequest] = useState('')
  const [model, setModel] = useState('gpt-4.1')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [result, setResult] = useState<AnalysisResult | null>(null)
  const [availableModels, setAvailableModels] = useState<Array<{
    id: string
    name: string
    display_name?: string
    provider: string
  }>>([])
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true)

  // 获取后端 URL
  const getBackendUrl = () => {
    return process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000'
  }

  // 获取可用模型列表
  const loadModels = async () => {
    try {
      const response = await fetch(`${getBackendUrl()}/models`)
      if (response.ok) {
        const data = await response.json()
        setAvailableModels(data.models || [])
      }
    } catch (error) {
      console.error('Failed to load models:', error)
    }
  }

  // 页面加载时获取模型列表
  useEffect(() => {
    loadModels()
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        toast.error('请选择CSV文件')
        return
      }
      setFile(selectedFile)
      toast.success(`已选择文件: ${selectedFile.name}`)
    }
  }

  const handleAnalyze = async () => {
    if (!file) {
      toast.error('请先选择CSV文件')
      return
    }

    if (!analysisRequest.trim()) {
      toast.error('请输入分析需求')
      return
    }

    setIsAnalyzing(true)
    setResult(null)

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('analysis_request', analysisRequest)
      formData.append('model', model)

      const response = await fetch(`${getBackendUrl()}/sandbox/analyze`, {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Analysis failed')
      }

      const result: AnalysisResult = await response.json()
      setResult(result)

      if (result.success) {
        toast.success('分析完成!')
      } else {
        toast.error('分析执行失败')
      }
    } catch (error) {
      console.error('Analysis error:', error)
      toast.error(`分析失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsAnalyzing(false)
    }
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <Sidebar 
        isCollapsed={sidebarCollapsed} 
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)} 
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0 overflow-hidden">
        <div className="flex-1 overflow-auto p-6">
          <div className="max-w-8xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 输入区域 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              数据分析配置
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 文件上传 */}
            <div>
              <Label htmlFor="file">CSV文件</Label>
              <Input
                id="file"
                type="file"
                accept=".csv"
                onChange={handleFileChange}
                className="mt-1"
              />
              {file && (
                <p className="text-sm text-green-600 mt-1">
                  已选择: {file.name} ({(file.size / 1024).toFixed(1)} KB)
                </p>
              )}
            </div>

            {/* 分析需求 */}
            <div>
              <Label htmlFor="analysis">分析需求</Label>
              <Textarea
                id="analysis"
                placeholder="请描述您希望进行的数据分析，例如：分析销售趋势、用户行为模式、数据分布情况等..."
                value={analysisRequest}
                onChange={(e) => setAnalysisRequest(e.target.value)}
                className="mt-1 min-h-[100px]"
              />
            </div>

            {/* 模型选择 */}
            <div>
              <Label htmlFor="model">AI模型</Label>
              <Select value={model} onValueChange={setModel}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="选择AI模型" />
                </SelectTrigger>
                <SelectContent>
                  {availableModels.map((modelItem) => (
                    <SelectItem key={modelItem.id} value={modelItem.id}>
                      {modelItem.display_name || modelItem.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 分析按钮 */}
            <Button
              onClick={handleAnalyze}
              disabled={!file || !analysisRequest.trim() || isAnalyzing}
              className="w-full"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  分析中...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  开始分析
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Jupyter Notebook 样式结果区域 */}
        <div className="space-y-4">
          {/* Notebook Content */}
          <div className="bg-white border rounded-lg shadow-sm">
            <div className="p-6">
              {!result && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Code className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">准备开始分析</h3>
                  <p className="text-gray-500">
                    上传CSV文件并配置分析需求后，这里将显示Jupyter Notebook样式的分析结果
                  </p>
                </div>
              )}

                             {result && (
                 <div className="space-y-1">
                   {/* 显示生成的代码 */}
                   {result.generated_code && (
                     <>
                       <CodeCell code={result.generated_code} index={1} />
                       
                       {/* 显示错误信息 */}
                       {!result.success && result.error && (
                         <ErrorCell error={result.error} />
                       )}

                       {/* 显示文本输出 */}
                       {result.output && result.output.map((output, index) => (
                         <OutputCell key={index} content={output} />
                       ))}

                       {/* 显示图片结果 */}
                       {result.images && result.images.map((image, index) => (
                         <ImageCell key={index} image={image} />
                       ))}

                       {/* 如果成功但没有输出 */}
                       {result.success && (!result.output || result.output.length === 0) && (!result.images || result.images.length === 0) && (
                         <div className="mb-3">
                           <div className="text-gray-600 italic">
                             代码执行成功，但没有输出内容
                           </div>
                         </div>
                       )}
                     </>
                   )}
                 </div>
               )}
            </div>
          </div>
        </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
