from typing import Dict, Optional, Any, TYPE_CHECKING
from dotenv import load_dotenv

from e2b_code_interpreter import Sandbox

if TYPE_CHECKING:
    from config.models import SandboxAnalyzeRequest

load_dotenv()

class E2BSandboxService:
    """E2B Sandbox服务，用于安全执行Python代码"""
    
    def __init__(self):
        self.sandbox: Optional[Sandbox] = None
    
    def create_sandbox(self) -> Sandbox:
        """创建sandbox实例"""
        if self.sandbox is None:
            # 设置较长的超时时间（10分钟）
            self.sandbox = Sandbox(timeout=600, api_key="e2b_fde740a7d2cdd325e98850b55c7c5c6fd8b13c14")
        return self.sandbox
    
    def recreate_sandbox(self) -> Sandbox:
        """重新创建sandbox实例（用于超时恢复）"""
        try:
            if self.sandbox:
                # E2B sandbox可能不需要显式关闭
                self.sandbox = None
        except:
            pass
        self.sandbox = Sandbox(timeout=600)
        print("✅ 沙箱已重新创建")
        return self.sandbox
    
    def upload_csv_file(self, file_content: bytes, filename: str = "dataset.csv") -> str:
        """上传CSV文件到sandbox"""
        try:
            sandbox = self.create_sandbox()
            
            # 将文件内容写入sandbox
            dataset_path = sandbox.files.write(filename, file_content)
            return dataset_path.path
        except Exception as e:
            if "sandbox was not found" in str(e) or "timeout" in str(e).lower():
                print("🔄 文件上传时沙箱超时，正在重新创建...")
                sandbox = self.recreate_sandbox()
                dataset_path = sandbox.files.write(filename, file_content)
                return dataset_path.path
            else:
                raise e
    
    def run_code(self, code: str) -> Dict[str, Any]:
        """执行Python代码并返回结果"""
        try:
            sandbox = self.create_sandbox()
            
            print('Running the code in the sandbox....')
            execution = sandbox.run_code(code)
            print('Code execution finished!')
        except Exception as e:
            if "sandbox was not found" in str(e) or "timeout" in str(e).lower():
                print("🔄 沙箱超时，正在重新创建...")
                sandbox = self.recreate_sandbox()
                print('重新运行代码...')
                execution = sandbox.run_code(code)
                print('代码执行完成!')
            else:
                raise e
        
        result = {
            "success": True,
            "output": [],
            "images": [],
            "error": None
        }
        
        # 检查是否有错误
        if execution.error:
            result["success"] = False
            result["error"] = {
                "name": execution.error.name,
                "value": execution.error.value,
                "traceback": execution.error.traceback
            }
            return result
        
        # 处理执行结果
        for i, exec_result in enumerate(execution.results):
            print(f"Processing result {i}: {type(exec_result)}")
            
            # 文本输出 - 包括 print 语句的输出
            if hasattr(exec_result, 'text') and exec_result.text:
                print(f"Found text output: {exec_result.text[:100]}...")
                result["output"].append(exec_result.text)
            
            # 标准输出流
            if hasattr(exec_result, 'stdout') and getattr(exec_result, 'stdout', None):
                stdout_content = getattr(exec_result, 'stdout')
                print(f"Found stdout: {stdout_content[:100]}...")
                result["output"].append(stdout_content)
                
            # 标准错误流
            if hasattr(exec_result, 'stderr') and getattr(exec_result, 'stderr', None):
                stderr_content = getattr(exec_result, 'stderr')
                print(f"Found stderr: {stderr_content[:100]}...")
                result["output"].append(f"Error: {stderr_content}")
            
            # 图片输出
            if hasattr(exec_result, 'png') and exec_result.png:
                print(f"Found image output {i}")
                # 图片以base64格式返回
                result["images"].append({
                    "index": i,
                    "data": exec_result.png,  # 已经是base64格式
                    "filename": f"chart-{i}.png"
                })
            
            # 其他类型的结果
            if hasattr(exec_result, 'html') and exec_result.html:
                print(f"Found HTML output")
                result["output"].append(f"HTML Content: {exec_result.html}")
                
            # 检查是否有其他属性
            for attr in dir(exec_result):
                if not attr.startswith('_') and attr not in ['text', 'stdout', 'stderr', 'png', 'html']:
                    attr_value = getattr(exec_result, attr, None)
                    if attr_value and not callable(attr_value):
                        print(f"Found {attr}: {str(attr_value)[:100]}...")
        
        print(f"Final result: success={result['success']}, outputs={len(result['output'])}, images={len(result['images'])}")
        return result
    
    async def analyze_csv_with_ai(self, request_data: 'SandboxAnalyzeRequest', model_client) -> Dict[str, Any]:
        """使用AI生成代码并分析CSV数据"""
        
        # 上传CSV文件
        dataset_path = self.upload_csv_file(request_data.file_content)
        
        # 构建分析提示词
        prompt = f"""
我有一个CSV文件，路径是 {dataset_path}。

用户的分析需求：{request_data.analysis_request}

请你：
1. 首先读取并分析CSV文件的结构（列名、数据类型、数据概览）
2. 根据用户需求编写Python代码进行数据分析
3. 生成合适的可视化图表（确保中文显示正常）
4. 输出关键的统计信息和发现

请直接编写Python代码，使用matplotlib、pandas、seaborn等库。确保代码可以直接运行。
代码应该包含：
- 中文字体配置（必须在开头设置）
- 数据读取和探索
- 数据清洗（如果需要）
- 数据分析
- 可视化（所有图表标题、标签都要支持中文）
- 关键发现的输出

**重要**：必须在代码开头设置中文字体支持，使用以下配置：

```python
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
plt.rcParams['figure.figsize'] = (15, 10)

# 读取CSV文件
df = pd.read_csv('{dataset_path}')

# 你的分析代码...
```

请只返回Python代码，不要包含其他解释文字。
记住文件路径是 {dataset_path}
"""
        
        # 使用模型客户端生成代码
        try:
            from autogen_core.models import UserMessage
            
            # 创建消息并调用模型
            messages = [UserMessage(content=prompt, source="user")]
            response = await model_client.create(messages)
            
            # 提取AI生成的代码
            ai_code = response.content

            print(f"Pre AI generated code:\n{ai_code}")
            
            # 清理代码（移除markdown标记等）
            if "```python" in ai_code:
                ai_code = ai_code.split("```python")[1].split("```")[0].strip()
            elif "```" in ai_code:
                ai_code = ai_code.split("```")[1].split("```")[0].strip()
            
            print(f"AI generated code:\n{ai_code}")
            
        except Exception as e:
            print(f"AI代码生成失败，使用默认模板: {e}")
            # 如果AI生成失败，使用简单的默认模板
            ai_code = f"""
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
plt.rcParams['figure.figsize'] = (15, 10)

# 读取CSV文件
df = pd.read_csv('{dataset_path}')

# 数据基本信息
print("=== 数据基本信息 ===")
print(f"数据形状: {{df.shape}}")
print(f"列名: {{df.columns.tolist()}}")
print(f"数据类型:\\n{{df.dtypes}}")

# 数据预览
print("\\n=== 数据预览 ===")
print(df.head())

# 基本统计信息
print("\\n=== 基本统计信息 ===")
print(df.describe())

# 用户需求分析
print("\\n=== 用户需求 ===")
print(f"分析需求: {request_data.analysis_request}")

# 生成高质量可视化
plt.style.use('default')  # 重置样式确保一致性
fig = plt.figure(figsize=(20, 16), dpi=300)  # 大尺寸高清图

numeric_columns = df.select_dtypes(include=[np.number]).columns

if len(numeric_columns) > 0:
    # 数据分布图
    plt.subplot(2, 3, 1)
    df[numeric_columns[0]].hist(bins=30, alpha=0.8, color='skyblue', edgecolor='black')
    plt.title(f'{{numeric_columns[0]}} 数据分布', fontsize=14, fontweight='bold')
    plt.xlabel(numeric_columns[0], fontsize=12)
    plt.ylabel('频数', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 箱线图
    plt.subplot(2, 3, 2)
    df[numeric_columns[0]].plot(kind='box', patch_artist=True)
    plt.title(f'{{numeric_columns[0]}} 箱线图', fontsize=14, fontweight='bold')
    plt.ylabel(numeric_columns[0], fontsize=12)
    plt.grid(True, alpha=0.3)
    
    if len(numeric_columns) > 1:
        # 散点图
        plt.subplot(2, 3, 3)
        plt.scatter(df[numeric_columns[0]], df[numeric_columns[1]], alpha=0.6, s=50, c='coral')
        plt.xlabel(numeric_columns[0], fontsize=12)
        plt.ylabel(numeric_columns[1], fontsize=12)
        plt.title(f'{{numeric_columns[0]}} vs {{numeric_columns[1]}} 关系图', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)

    # 如果有更多数值列，显示相关性热力图
    if len(numeric_columns) > 1:
        plt.subplot(2, 3, 4)
        correlation_matrix = df[numeric_columns].corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='RdYlBu_r', center=0, fmt='.2f', 
                   square=True, cbar_kws={{"shrink": 0.8}})
        plt.title('数值变量相关性热力图', fontsize=14, fontweight='bold')
        
    # 密度图
    if len(numeric_columns) > 0:
        plt.subplot(2, 3, 5)
        df[numeric_columns[0]].plot(kind='density', color='green', linewidth=2)
        plt.title(f'{{numeric_columns[0]}} 密度分布', fontsize=14, fontweight='bold')
        plt.xlabel(numeric_columns[0], fontsize=12)
        plt.ylabel('密度', fontsize=12)
        plt.grid(True, alpha=0.3)
        
    # 统计摘要文本
    plt.subplot(2, 3, 6)
    stats_text = f'''
    数据统计摘要
    
    样本数量: {{len(df)}}
    数值列数: {{len(numeric_columns)}}
    
    {{numeric_columns[0]}} 统计:
    均值: {{df[numeric_columns[0]].mean():.2f}}
    中位数: {{df[numeric_columns[0]].median():.2f}}
    标准差: {{df[numeric_columns[0]].std():.2f}}
    最小值: {{df[numeric_columns[0]].min():.2f}}
    最大值: {{df[numeric_columns[0]].max():.2f}}
    '''
    plt.text(0.1, 0.9, stats_text, transform=plt.gca().transAxes, 
             fontsize=11, verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.5))
    plt.axis('off')
    plt.title('统计摘要', fontsize=14, fontweight='bold')

plt.suptitle(f'数据分析报告 - {{len(df)}}行数据', fontsize=16, fontweight='bold', y=0.95)
plt.tight_layout()
plt.subplots_adjust(top=0.92)
plt.show()

print("\\n=== 分析完成 ===")
print("✅ 高清图表生成完成（DPI=300）")
print("✅ 中文字体显示已优化")
"""
        
        try:
            # 执行代码
            result = self.run_code(ai_code)
            result["generated_code"] = ai_code
            result["dataset_path"] = dataset_path
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": {
                    "name": "Execution Error", 
                    "value": str(e),
                    "traceback": ""
                },
                "output": [],
                "images": [],
                "generated_code": ai_code,
                "dataset_path": dataset_path
            }
    
    def close(self):
        """关闭sandbox"""
        if self.sandbox:
            self.sandbox = None
    
    def __del__(self):
        """析构函数，确保sandbox被正确关闭"""
        self.close()


# 全局sandbox服务实例
sandbox_service = E2BSandboxService()


def get_sandbox_service() -> E2BSandboxService:
    """获取sandbox服务实例"""
    return sandbox_service
