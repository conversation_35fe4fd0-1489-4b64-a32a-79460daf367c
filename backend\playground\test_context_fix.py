#!/usr/bin/env python3
"""
测试上下文修复功能
"""
import asyncio
import json
from config.models import ExecutionPlan, PlanStep, ExecutePlanRequest
from agent.llm_chat import LLMChat

async def test_context_fix():
    """测试执行计划时是否正确传递原始消息"""
    
    # 创建一个测试执行计划
    test_plan = ExecutionPlan(
        use_mcp="yes",
        reason="查询北京天气信息",
        plan=[
            PlanStep(
                step=1,
                action="call_mcp",
                server="amap-maps",
                description="调用高德地图API查询北京天气",
                status="pending"
            )
        ]
    )
    
    # 原始用户消息
    original_message = "请帮我查询一下北京今天的天气怎么样"
    
    print("🧪 测试上下文修复功能")
    print(f"📝 原始消息: {original_message}")
    print(f"📋 计划原因: {test_plan.reason}")
    print()
    
    try:
        print("🚀 开始执行计划...")
        async for chunk in LLMChat.execute_plan_stream(
            plan=test_plan,
            mcp_names=["amap-maps"],
            model_name="deepseek-r1-0528",
            original_message=original_message
        ):
            # 解析并显示流式数据
            if chunk.startswith('data: '):
                try:
                    data = json.loads(chunk[6:])
                    chunk_type = data.get('type', 'unknown')
                    
                    if chunk_type == 'step_start':
                        print(f"🔄 开始步骤 {data.get('step')}: {data.get('action')}")
                    elif chunk_type == 'step_complete':
                        print(f"✅ 完成步骤 {data.get('step')}")
                    elif chunk_type == 'message_delta':
                        print(data.get('content', ''), end='')
                    elif chunk_type == 'end':
                        print(f"\n🎉 {data.get('content', '执行完成')}")
                        break
                    elif chunk_type == 'error':
                        print(f"\n❌ 错误: {data.get('content')}")
                        break
                except json.JSONDecodeError:
                    pass
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_context_fix())
