#!/usr/bin/env python3
"""
测试计划确认流程
"""

import asyncio
import json
from agent.llm_chat import LLMChat

async def test_plan_confirmation_flow():
    """测试计划确认流程"""
    
    print("🧪 测试计划确认流程")
    print("=" * 50)
    
    # 模拟用户发送消息
    user_message = "分析北京天气并生成图表"
    mcp_servers = ["amap-maps", "quickchart-server"]
    model_name = "deepseek-r1-0528"
    
    print(f"📝 用户消息: {user_message}")
    print(f"🔧 MCP服务器: {mcp_servers}")
    print(f"🤖 模型: {model_name}")
    print()
    
    try:
        print("🚀 开始处理消息...")
        
        # 调用chat_stream，应该只返回执行计划，不执行
        async for chunk in LLMChat.chat_stream(user_message, mcp_servers, model_name):
            if chunk.startswith('data: '):
                try:
                    data = json.loads(chunk[6:])
                    chunk_type = data.get('type', 'unknown')
                    
                    if chunk_type == 'execution_plan':
                        print("📋 收到执行计划:")
                        plan = data.get('plan', {})
                        print(f"   - 使用MCP: {plan.get('use_mcp')}")
                        print(f"   - 原因: {plan.get('reason')}")
                        print(f"   - 步骤数: {len(plan.get('plan', []))}")
                        for i, step in enumerate(plan.get('plan', []), 1):
                            print(f"     {i}. {step.get('action')} - {step.get('description')}")
                    
                    elif chunk_type == 'waiting_confirmation':
                        print("⏳ 收到等待确认消息:")
                        print(f"   {data.get('message', '等待用户确认')}")
                    
                    elif chunk_type == 'end':
                        print("✅ 流程结束:")
                        print(f"   {data.get('content', '完成')}")
                        break
                    
                    elif chunk_type == 'error':
                        print("❌ 错误:")
                        print(f"   {data.get('content')}")
                        break
                    
                    elif chunk_type == 'message_delta':
                        # 如果有消息内容，说明系统在执行而不是等待确认
                        print("⚠️  警告: 系统正在执行而不是等待确认!")
                        print(f"   内容: {data.get('content', '')}")
                        
                except json.JSONDecodeError:
                    pass
                    
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def test_direct_execution():
    """测试没有MCP服务器时的直接执行"""
    
    print("\n🧪 测试直接执行流程（无MCP）")
    print("=" * 50)
    
    user_message = "你好，请介绍一下自己"
    mcp_servers = []  # 没有MCP服务器
    model_name = "deepseek-r1-0528"
    
    print(f"📝 用户消息: {user_message}")
    print(f"🔧 MCP服务器: {mcp_servers} (空)")
    print()
    
    try:
        print("🚀 开始处理消息...")
        
        async for chunk in LLMChat.chat_stream(user_message, mcp_servers, model_name):
            if chunk.startswith('data: '):
                try:
                    data = json.loads(chunk[6:])
                    chunk_type = data.get('type', 'unknown')
                    
                    if chunk_type == 'execution_plan':
                        print("📋 收到执行计划:")
                        plan = data.get('plan', {})
                        print(f"   - 使用MCP: {plan.get('use_mcp')}")
                        print(f"   - 原因: {plan.get('reason')}")
                    
                    elif chunk_type == 'message_delta':
                        print(data.get('content', ''), end='')
                    
                    elif chunk_type == 'end':
                        print(f"\n✅ 流程结束: {data.get('content', '完成')}")
                        break
                        
                except json.JSONDecodeError:
                    pass
                    
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_plan_confirmation_flow())
    asyncio.run(test_direct_execution())
    
    print("\n✅ 所有测试完成")
    print("\n📝 预期结果:")
    print("1. 有MCP服务器时：只显示执行计划，等待用户确认")
    print("2. 无MCP服务器时：可能直接执行或显示简单计划")
