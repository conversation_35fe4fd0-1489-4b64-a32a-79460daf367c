'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Copy, Download, X, Code, Eye } from 'lucide-react'
import toast from 'react-hot-toast'
import { type ArtifactData } from '@/lib/Parser/ArtifactsParser'
import ReactRenderer from '../lib/Renderer/ReactRenderer'
import SVGRenderer from '../lib/Renderer/SVGRenderer'
import CodeViewRenderer from '@/lib/Renderer/CodeViewRenderer'

interface ArtifactsDisplayProps {
  artifacts: ArtifactData[]
  onClose: () => void
  streamingContent?: string
  isStreaming?: boolean
}

export default function ArtifactsDisplay({ artifacts, onClose, streamingContent, isStreaming }: ArtifactsDisplayProps) {
  const [selectedArtifact, setSelectedArtifact] = useState<ArtifactData | null>(
    artifacts.length > 0 ? artifacts[0] : null
  )
  const [viewMode, setViewMode] = useState<'code' | 'preview'>('preview')

  useEffect(() => {
    if (artifacts.length > 0 && !selectedArtifact) {
      setSelectedArtifact(artifacts[0])
    }
  }, [artifacts, selectedArtifact])

  // 复制流式内容
  const copyStreamingContent = async () => {
    try {
      await navigator.clipboard.writeText(streamingContent || '')
      toast.success('已复制到剪贴板')
    } catch {
      toast.error('复制失败')
    }
  }

  // 如果没有 artifacts，显示流式内容
  if (artifacts.length === 0) {
    return (
      <div className="h-full flex flex-col bg-white border-l border-gray-200">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-semibold">React 组件</h3>
            <span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">
              {isStreaming ? '实时输出' : '输出完成'}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            {/* 当流式输出完成且有内容时显示复制按钮 */}
            {!isStreaming && streamingContent && (
              <Button variant="outline" size="sm" onClick={copyStreamingContent}>
                <Copy className="w-4 h-4 mr-1" />
                复制
              </Button>
            )}
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* 实时流式内容显示 */}
        <div className="flex-1 overflow-hidden">
          {streamingContent ? (
            <CodeViewRenderer 
              code={streamingContent}
              language="jsx"
            />
          ) : (
            <div className="h-full flex items-center justify-center bg-gray-900 text-gray-400">
              <p>等待接收组件代码...</p>
            </div>
          )}
        </div>
      </div>
    )
  }

  if (!selectedArtifact) return null

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(selectedArtifact.content)
      toast.success('已复制到剪贴板')
    } catch {
      toast.error('复制失败')
    }
  }

  const downloadArtifact = () => {
    const blob = new Blob([selectedArtifact.content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${selectedArtifact.title || 'artifact'}.${getFileExtension(selectedArtifact.type)}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('文件已下载')
  }

  const getFileExtension = (type: string) => {
    const extensions: Record<string, string> = {
      'html': 'html',
      'svg': 'svg',
      'javascript': 'js',
      'python': 'py',
      'react': 'jsx',
      'markdown': 'md',
      'text': 'txt',
      'json': 'json',
      'css': 'css'
    }
    return extensions[type] || 'txt'
  }

  const renderPreview = () => {
    switch (selectedArtifact.type) {
      case 'html':
        return (
          <iframe
            srcDoc={selectedArtifact.content}
            className="w-full h-full border-none"
            sandbox="allow-scripts allow-same-origin"
            title="HTML Preview"
          />
        )
      case 'svg':
        return <SVGRenderer content={selectedArtifact.content} />
      case 'react':
        return <ReactRenderer code={selectedArtifact.content} />
      case 'markdown':
        return (
          <div className="prose max-w-none p-4 overflow-auto h-full">
            <div dangerouslySetInnerHTML={{ __html: selectedArtifact.content }} />
          </div>
        )
      case 'json':
        try {
          const formatted = JSON.stringify(JSON.parse(selectedArtifact.content), null, 2)
          return (
            <CodeViewRenderer 
              code={formatted}
              language="json"
            />
          )
        } catch {
          return (
            <CodeViewRenderer 
              code={selectedArtifact.content}
              language="text"
            />
          )
        }
      default:
        return (
          <CodeViewRenderer 
            code={selectedArtifact.content}
            language="text"
          />
        )
    }
  }

  const renderCodeView = () => {
    const getLanguage = (type: string) => {
      const languageMap: Record<string, string> = {
        'html': 'html',
        'javascript': 'javascript',
        'react': 'jsx',
        'python': 'python',
        'json': 'json',
        'css': 'css',
        'markdown': 'markdown',
        'svg': 'xml',
        'text': 'text'
      }
      return languageMap[type] || 'text'
    }

    return (
      <CodeViewRenderer 
        code={selectedArtifact.content}
        language={getLanguage(selectedArtifact.type)}
      />
    )
  }

  const canPreview = ['html', 'svg', 'markdown', 'json', 'react'].includes(selectedArtifact.type)

  return (
    <div className="h-full flex flex-col bg-white border-l border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <h3 className="text-lg font-semibold">
            {selectedArtifact.title || 'Artifact'}
          </h3>
          <span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">
            {selectedArtifact.type}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          {canPreview && (
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <Button
                variant={viewMode === 'preview' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('preview')}
                className="px-3 py-1 h-8"
              >
                <Eye className="w-4 h-4 mr-1" />
                预览
              </Button>
              <Button
                variant={viewMode === 'code' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('code')}
                className="px-3 py-1 h-8"
              >
                <Code className="w-4 h-4 mr-1" />
                代码
              </Button>
            </div>
          )}
          <Button variant="outline" size="sm" onClick={copyToClipboard}>
            <Copy className="w-4 h-4" />
            
          </Button>
          <Button variant="outline" size="sm" onClick={downloadArtifact}>
            <Download className="w-4 h-4" />

          </Button>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Artifact selector (if multiple artifacts) */}
      {artifacts.length > 1 && (
        <div className="p-3 border-b border-gray-200">
          <div className="flex space-x-2 overflow-x-auto">
            {artifacts.map((artifact, index) => (
              <button
                key={artifact.id}
                onClick={() => setSelectedArtifact(artifact)}
                className={`px-3 py-2 text-sm rounded-lg whitespace-nowrap ${
                  selectedArtifact?.id === artifact.id
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {artifact.title || `Artifact ${index + 1}`}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'preview' && canPreview ? renderPreview() : renderCodeView()}
      </div>
    </div>
  )
} 