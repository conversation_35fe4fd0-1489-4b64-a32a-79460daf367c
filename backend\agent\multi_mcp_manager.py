import json
import re
from typing import List, Dict, Optional
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.tools.mcp import McpWorkbench, mcp_server_tools
from config.prompt import create_system_message, create_artifacts_instructions
from agent.utils import get_mcp_server_params
from agent.planner import Planner
from agent.llm_service import ModelManager
from agent.task_evaluator import AdaptivePlanner  # 新增导入
import os

class MultiMcpManager:
    """多MCP服务器管理器 - 新架构：Planner负责规划，Agent负责执行"""
    
    def __init__(self, model_name: str):
        self.workbenches: Dict[str, McpWorkbench] = {}
        self.agents: Dict[str, AssistantAgent] = {}
        self.available_tools: Dict[str, List[str]] = {}  # 服务器名 -> 工具列表
        self.fallback_agent: Optional[AssistantAgent] = None  # 无MCP工具的后备代理
        self.planner = Planner(model_name)  # AI规划器
        self.adaptive_planner = AdaptivePlanner(model_name)  # 自适应规划器
        self.model_name = model_name
        self.preset_execution_plan = None  # 预设的执行计划
    
    def _enhance_agent_with_artifacts(self, agent: AssistantAgent, mcp_name: str = None) -> AssistantAgent:
        """为Agent添加Artifacts功能的系统消息"""
        base_message = create_system_message(mcp_name) if mcp_name else "你是一个AI助手，可以回答各种问题并进行对话。"
        enhanced_message = base_message + create_artifacts_instructions()
        agent.system_message = enhanced_message
        return agent
    
    async def initialize_servers(self, mcp_names: List[str], model_name: str):
        """初始化多个MCP服务器"""
        model_manager = ModelManager()
        
        # 使用新的 create_client 方法创建客户端
        try:
            model_client = model_manager.create_client(model_name)
            print(f"✅ 成功创建模型客户端: {model_name}")
        except Exception as e:
            raise ValueError(f"创建模型客户端失败 {model_name}: {e}")
        
        # 1. 用户未选择使用MCP服务器，创建一个基础代理
        if not mcp_names:
            self.fallback_agent = AssistantAgent(
                name="assistant_base",
                model_client=model_client,
                system_message="你是一个AI助手，可以回答各种问题并进行对话。",
                reflect_on_tool_use=False,
                model_client_stream=True,
            )
            self.fallback_agent = self._enhance_agent_with_artifacts(self.fallback_agent)
            print(f"✅ 创建基础AI代理（无MCP工具）- 使用模型: {model_name}")
            return
        
        # 2. 用户选择使用MCP服务器，创建多MCP代理
        for mcp_name in mcp_names:
            try:
                # 2.1 创建workbench
                server_params = get_mcp_server_params(mcp_name)
                tools = await mcp_server_tools(server_params)
                self.available_tools[mcp_name] = [tool.name for tool in tools]
                workbench = McpWorkbench(server_params)
                await workbench.__aenter__()
                self.workbenches[mcp_name] = workbench
                
                # 创建有效的agent名称（替换连字符为下划线）
                agent_name = f"assistant_{mcp_name.replace('-', '_')}"
                
                # 创建专门的代理
                agent = AssistantAgent(
                    name=agent_name,
                    model_client=model_client,
                    system_message=create_system_message(mcp_name),
                    workbench=workbench,
                    reflect_on_tool_use=False if 'claude' in model_name.lower() else True,  # Claude模型不使用反思
                    model_client_stream=True,
                )
                # 为Agent添加Artifacts功能
                agent = self._enhance_agent_with_artifacts(agent, mcp_name)
                self.agents[mcp_name] = agent
                
                print(f"✅ 成功初始化MCP服务器: {mcp_name} - 使用模型: {model_name}")
                
            except Exception as e:
                print(f"❌ 初始化MCP服务器失败 {mcp_name}: {e}")
        
        # 如果没有成功初始化任何MCP服务器，创建后备代理
        if not self.workbenches and not self.fallback_agent:
            self.fallback_agent = AssistantAgent(
                name="assistant_base",
                model_client=model_client,
                system_message="你是一个AI助手，可以回答各种问题并进行对话。",
                reflect_on_tool_use=False,
                model_client_stream=True,
            )
            self.fallback_agent = self._enhance_agent_with_artifacts(self.fallback_agent)
            print(f"✅ 创建基础AI代理（MCP服务器初始化失败后的后备方案）- 使用模型: {model_name}")

    def set_execution_plan(self, plan: Dict):
        """设置预设的执行计划，跳过规划阶段"""
        self.preset_execution_plan = plan
        print(f"🎯 设置预设执行计划: {plan.get('reason', '用户确认的计划')}")

    async def get_server_for_task(self, message: str) -> Dict:
        """Planner负责：根据用户消息进行智能规划，制定详细的执行计划"""

        # 如果有预设的执行计划，直接返回
        if self.preset_execution_plan:
            print("🎯 使用预设执行计划，跳过规划阶段")
            plan = self.preset_execution_plan
            self.preset_execution_plan = None  # 使用后清除
            return plan

        print("🧠 Planner开始分析任务...")

        if not self.workbenches:
            return {
                "use_mcp": "no",
                "reason": "没有可用的MCP服务器",
                "plan": [
                    {
                        "step": 1,
                        "action": "direct_response",
                        "description": "直接回答用户问题",
                        "task_prompt": message
                    }
                ]
            }

        available_servers = list(self.workbenches.keys())
        plan = await self.planner.intent_recognition(message, available_servers)

        print(f"🎯 Planner规划完成: {plan.get('reason', '智能规划')}")
        print(f"📋 执行计划: {len(plan.get('plan', []))} 个步骤")

        return plan
    
    async def route_message(self, message: str):
        """新架构：Planner规划，Agent执行"""
        
        # Step 1: Planner制定执行计划
        execution_plan = await self.get_server_for_task(message)
        
        # Step 2: 如果没有MCP工作台但有后备代理，使用后备代理
        if not self.workbenches and self.fallback_agent:
            async for chunk in self._execute_with_agent(self.fallback_agent, message):
                yield chunk
            return
        
        # Step 3: 如果既没有工作台也没有后备代理，抛出异常
        if not self.workbenches and not self.fallback_agent:

            raise Exception("没有可用的AI代理")
        
        # Step 4: 如果计划决定不使用MCP，使用后备代理
        if execution_plan.get("use_mcp") == "no":

            if self.fallback_agent:
                async for chunk in self._execute_with_agent(self.fallback_agent, message):

                    yield chunk
            else:
                # 强制使用第一个可用的MCP代理
                first_server = list(self.agents.keys())[0]
                agent = self.agents[first_server]
                async for chunk in self._execute_with_agent(agent, message):
                    yield chunk
            return
        
        # Step 5: 按照Planner的执行计划逐步执行
        plan_steps = execution_plan.get("plan", [])
        step_results = {}  # 存储每个步骤的结果数据
        
        
        for step_index, step in enumerate(plan_steps):
            step_number = step.get('step', step_index + 1)
            action = step.get('action')
            server = step.get('server')
            description = step.get('description', '')
            task_prompt = step.get('task_prompt', message)
            depends_on = step.get('depends_on', [])
            
            # 发送步骤开始信号
            step_start_data = {
                "type": "step_start",
                "step": step_number,
                "action": action,
                "source": "system"
            }
            yield f"data: {json.dumps(step_start_data, ensure_ascii=False)}\n\n"
            
            # 为多步骤任务添加步骤分隔符
            if step_number > 1:
                separator_content = f"\n\n---\n\n**步骤 {step_number}: {description}**\n\n"
                separator_data = {
                    "type": "message_delta",
                    "content": separator_content,
                    "source": "assistant"
                }

                yield f"data: {json.dumps(separator_data, ensure_ascii=False)}\n\n"
            
            # 根据action类型选择合适的Agent执行
            if action == "call_mcp" and server and server in self.agents:

                agent = self.agents[server]
                step_result = ""
                
                async for chunk in self._execute_with_agent(agent, task_prompt, step_number):
                    if isinstance(chunk, str) and chunk.startswith('step_result:'):
                        step_result = chunk.replace('step_result:', '')

                    else:

                        yield chunk
                
                # 存储步骤结果
                if step_result.strip():
                    step_results[step_number] = {
                        "action": action,
                        "content": step_result.strip()
                    }

            
            elif action == "summary":

                # 执行总结步骤
                step_result = ""
                
                async for chunk in self._execute_summary_step(step, step_results, step_number):
                    if isinstance(chunk, str) and chunk.startswith('step_result:'):
                        step_result = chunk.replace('step_result:', '')

                    else:

                        yield chunk
                
                # 存储步骤结果
                if step_result.strip():
                    step_results[step_number] = {
                        "action": action,
                        "content": step_result.strip()
                    }
            
            elif action == "direct_response":

                # 执行直接回答步骤
                response_agent = self.fallback_agent or list(self.agents.values())[0]
                step_result = ""
                
                async for chunk in self._execute_with_agent(response_agent, task_prompt, step_number):
                    if isinstance(chunk, str) and chunk.startswith('step_result:'):
                        step_result = chunk.replace('step_result:', '')

                    else:

                        yield chunk
                
                # 存储步骤结果
                if step_result.strip():
                    step_results[step_number] = {
                        "action": action,
                        "content": step_result.strip()
                    }

            
            # 发送步骤完成信号
            step_complete_data = {
                "type": "step_complete",
                "step": step_number,
                "action": action,
                "source": "system"
            }

            yield f"data: {json.dumps(step_complete_data, ensure_ascii=False)}\n\n"
        
        # 🚀 新增：智能任务完成评估
        if plan_steps and step_results:

            
            try:
                # 评估任务是否真正完成
                continue_decision = await self.adaptive_planner.should_continue_execution(
                    original_request=message,
                    execution_plan=execution_plan,
                    step_results=step_results
                )
                
                evaluation = continue_decision.get("evaluation", {})
                completion_score = evaluation.get("completion_score", 0)
                
                
                
                # 发送评估结果给前端
                evaluation_data = {
                    "type": "task_evaluation",
                    "completion_score": completion_score,
                    "is_complete": evaluation.get("is_complete", False),
                    "summary": evaluation.get("summary", ""),
                    "missing_elements": evaluation.get("missing_elements", []),
                    "should_continue": continue_decision.get("should_continue", False),
                    "source": "system"
                }
                yield f"data: {json.dumps(evaluation_data, ensure_ascii=False)}\n\n"
                
                # 如果需要继续执行额外步骤
                if continue_decision.get("should_continue", False):
                    additional_steps = continue_decision.get("additional_steps", [])

                    
                    # 发送额外步骤开始信号
                    extra_steps_data = {
                        "type": "extra_steps_start",
                        "reason": continue_decision.get("reason", "需要额外步骤完善结果"),
                        "steps_count": len(additional_steps),
                        "source": "system"
                    }
                    yield f"data: {json.dumps(extra_steps_data, ensure_ascii=False)}\n\n"
                    
                    # 执行额外步骤
                    max_step_number = max(step_results.keys()) if step_results else 0
                    
                    for extra_idx, extra_step in enumerate(additional_steps):
                        extra_step_number = max_step_number + extra_idx + 1
                        action = extra_step.get("action", "direct_response")
                        description = extra_step.get("description", "额外完善步骤")
                        reason = extra_step.get("reason", "")
                        

                        
                        # 发送额外步骤开始信号
                        extra_step_start_data = {
                            "type": "step_start",
                            "step": extra_step_number,
                            "action": action,
                            "is_extra": True,
                            "reason": reason,
                            "source": "system"
                        }
                        yield f"data: {json.dumps(extra_step_start_data, ensure_ascii=False)}\n\n"
                        
                        # 添加步骤分隔符
                        separator_content = f"\n\n---\n\n**额外步骤 {extra_step_number}: {description}**\n补充说明: {reason}\n\n"
                        separator_data = {
                            "type": "message_delta",
                            "content": separator_content,
                            "source": "assistant"
                        }
                        yield f"data: {json.dumps(separator_data, ensure_ascii=False)}\n\n"
                        
                        # 根据action类型执行额外步骤
                        extra_step_result = ""
                        
                        if action == "call_mcp":
                            # 选择合适的MCP服务器（这里简化为使用第一个可用的）
                            if self.agents:
                                agent = list(self.agents.values())[0]
                                task_prompt = f"根据前面的执行结果完善任务：{description}。具体要求：{reason}"
                                
                                async for chunk in self._execute_with_agent(agent, task_prompt, extra_step_number):
                                    if isinstance(chunk, str) and chunk.startswith('step_result:'):
                                        extra_step_result = chunk.replace('step_result:', '')
                                    else:
                                        yield chunk
                        
                        elif action == "summary":
                            # 执行总结步骤
                            summary_agent = self.fallback_agent or list(self.agents.values())[0] if self.agents else None
                            if summary_agent:
                                task_prompt = f"对以下执行结果进行总结：{description}。具体要求：{reason}"
                                
                                async for chunk in self._execute_with_agent(summary_agent, task_prompt, extra_step_number):
                                    if isinstance(chunk, str) and chunk.startswith('step_result:'):
                                        extra_step_result = chunk.replace('step_result:', '')
                                    else:
                                        yield chunk
                        
                        else:  # direct_response
                            response_agent = self.fallback_agent or list(self.agents.values())[0] if self.agents else None
                            if response_agent:
                                task_prompt = f"补充完善回答：{description}。具体要求：{reason}"
                                
                                async for chunk in self._execute_with_agent(response_agent, task_prompt, extra_step_number):
                                    if isinstance(chunk, str) and chunk.startswith('step_result:'):
                                        extra_step_result = chunk.replace('step_result:', '')
                                    else:
                                        yield chunk
                        
                        # 存储额外步骤结果
                        if extra_step_result.strip():
                            step_results[extra_step_number] = {
                                "action": action,
                                "content": extra_step_result.strip(),
                                "is_extra": True
                            }
                        
                        # 发送额外步骤完成信号
                        extra_step_complete_data = {
                            "type": "step_complete",
                            "step": extra_step_number,
                            "action": action,
                            "is_extra": True,
                            "source": "system"
                        }
                        yield f"data: {json.dumps(extra_step_complete_data, ensure_ascii=False)}\n\n"
                    
                    # 最终完成评估
                    
                    final_evaluation_data = {
                        "type": "task_evaluation_final",
                        "message": "任务经过智能评估和额外步骤完善，现已完成",
                        "total_steps": len(step_results),
                        "extra_steps_count": len(additional_steps),
                        "source": "system"
                    }
                    yield f"data: {json.dumps(final_evaluation_data, ensure_ascii=False)}\n\n"
                
                else:
                    
                    # 发送任务完成信号
                    task_complete_data = {
                        "type": "task_complete",
                        "completion_score": completion_score,
                        "summary": evaluation.get("summary", "任务已成功完成"),
                        "total_steps": len(step_results),
                        "source": "system"
                    }
                    yield f"data: {json.dumps(task_complete_data, ensure_ascii=False)}\n\n"
            
            except Exception as e:
                # 评估失败时发送默认完成信号
                default_complete_data = {
                    "type": "task_complete",
                    "completion_score": 70,
                    "summary": "原计划执行完成，评估系统出现问题",
                    "total_steps": len(step_results),
                    "source": "system"
                }
                yield f"data: {json.dumps(default_complete_data, ensure_ascii=False)}\n\n"
        
        # 如果执行计划中没有任何步骤被执行，使用默认处理
        if not plan_steps:

            if self.agents:
                first_server = list(self.agents.keys())[0]

                agent = self.agents[first_server]
                async for chunk in self._execute_with_agent(agent, message):
                    
                    yield chunk
            elif self.fallback_agent:

                async for chunk in self._execute_with_agent(self.fallback_agent, message):
                    yield chunk
    
    async def _execute_with_agent(self, agent: AssistantAgent, task_prompt: str, step_number: int = None):
        """Agent执行任务的核心方法"""
        step_result = ""
        
        
        try:
            async for chunk in agent.run_stream(task=task_prompt):
                chunk_type = getattr(chunk, 'type', '')

                
                # 处理工具调用请求事件
                if chunk_type == 'ToolCallRequestEvent' and hasattr(chunk, 'content'):    
                    for function_call in chunk.content:
                        try:
                            tool_call_id = getattr(function_call, 'id', f"tool_{step_number or 'main'}")
                            tool_name = getattr(function_call, 'name', 'unknown')
                            arguments_str = getattr(function_call, 'arguments', '{}')
                                                       
                            try:
                                parameters = json.loads(arguments_str)
                            except:
                                parameters = {"raw_arguments": arguments_str}
                            
                            data = {
                                "type": "tool_call_start",
                                "tool_call_id": tool_call_id,
                                "tool_name": tool_name,
                                "parameters": parameters,
                                "source": "system",
                                "step": step_number
                            }
                            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                        except Exception as e:
                            print(f"⚠️ [DEBUG] 解析工具调用请求失败: {e}")
                
                # 处理工具调用执行结果事件
                elif chunk_type == 'ToolCallExecutionEvent' and hasattr(chunk, 'content'):

                    for execution_result in chunk.content:
                        try:
                            tool_call_id = getattr(execution_result, 'call_id', 'unknown')
                            result_content = getattr(execution_result, 'content', '')
                            is_error = getattr(execution_result, 'is_error', False)

                            
                            try:
                                parsed_result = json.loads(result_content)

                            except:
                                parsed_result = result_content

                            
                            data = {
                                "type": "tool_call_result",
                                "tool_call_id": tool_call_id,
                                "result": parsed_result,
                                "is_error": is_error,
                                "source": "system",
                                "step": step_number
                            }
                            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                        except Exception as e:
                            print(f"⚠️ [DEBUG] 解析工具调用结果失败: {e}")
                
                elif chunk_type == 'ModelClientStreamingChunkEvent' and hasattr(chunk, 'content'):
                    # 流式增量内容
                    content = str(chunk.content)
                    step_result += content
                    
                    
                    data = {
                        "type": "message_delta",
                        "content": content,
                        "source": "assistant"
                    }
                    yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                    
                elif chunk_type == 'TextMessage' and hasattr(chunk, 'content'):
                    # 完整消息内容 - 只记录到step_result，不再重复输出
                    content = str(chunk.content).strip()

                    if content:
                        step_result = content
                        # 注意：不再输出message_delta，避免重复显示
                elif isinstance(chunk, str) and chunk.startswith('data: '):
                    # 直接转发数据
                    yield chunk
        
        except Exception as e:
            # 处理Agent执行错误
            error_message = f"Agent执行出现错误: {str(e)}"
            
            error_data = {
                "type": "message_delta",
                "content": f"\n⚠️ {error_message}\n\n",
                "source": "assistant"
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
            
            step_result = f"Agent执行出错: {error_message}"
        
        # 返回步骤结果供上层使用
        yield f"step_result:{step_result}"
    
    async def _execute_summary_step(self, step: Dict, step_results: Dict, step_number: int):
        """执行总结步骤"""
        depends_on = step.get('depends_on', [])
        task_prompt = step.get('task_prompt', '')
        
        # 收集需要总结的内容
        target_results = []
        if depends_on:
            # 总结特定的依赖步骤
            for dep_step in depends_on:
                if dep_step in step_results:
                    target_results.append(step_results[dep_step]["content"])
        else:
            # 总结所有MCP调用的结果
            for step_num, result in step_results.items():
                if result["action"] == "call_mcp":
                    target_results.append(result["content"])
        
        if target_results:
            # 构建总结提示
            if task_prompt and task_prompt != step.get('description', ''):
                summary_prompt = f"{task_prompt}\n\n需要总结的信息:\n{chr(10).join(target_results)}"
            else:
                summary_prompt = f"请对以下信息进行简洁的总结：\n\n{chr(10).join(target_results)}\n\n请提供一个清晰、简洁的总结。"
            
            # 使用最合适的代理进行总结
            summary_agent = self.fallback_agent or list(self.agents.values())[0] if self.agents else None
            
            if summary_agent:
                step_result = ""
                
                async for chunk in self._execute_with_agent(summary_agent, summary_prompt, step_number):
                    if isinstance(chunk, str) and chunk.startswith('step_result:'):
                        step_result = chunk.replace('step_result:', '')
                    else:
                        yield chunk
                
                # 存储总结结果
                if step_result.strip():
                    step_results[step_number] = {
                        "action": "summary",
                        "content": step_result.strip()
                    }
                
                # 返回step_result供上层使用
                yield f"step_result:{step_result}"
    
    async def cleanup(self):
        """清理所有工作台连接"""
        for workbench in self.workbenches.values():
            try:
                await workbench.__aexit__(None, None, None)
            except:
                pass
        self.workbenches.clear()
        self.agents.clear()


class SmartMultiMcpAgent:
    """智能多MCP代理，新架构：Planner规划+Agent执行"""
    
    def __init__(self, mcp_names: List[str], model_name: str):
        self.mcp_names = mcp_names
        self.model_name = model_name
        self.manager = MultiMcpManager(model_name)  # 传递model_name参数
        self._initialized = False
    
    async def initialize(self):
        """初始化所有MCP服务器"""
        if not self._initialized:
            await self.manager.initialize_servers(self.mcp_names, self.model_name)
            self._initialized = True
    
    async def run_stream(self, task: str):
        """运行流式任务 - 新架构：Planner规划+Agent执行"""
        if not self._initialized:
            await self.initialize()
        
        print("="*50)
        print("🎯 新架构启动：Planner规划 + Agent执行")
        print("="*50)
        
        async for chunk in self.manager.route_message(task):
            yield chunk
    
    async def cleanup(self):
        """清理资源"""
        await self.manager.cleanup()
        self._initialized = False


async def create_smart_multi_mcp_agent(mcp_names: List[str], model_name: str):
    """创建智能多MCP代理"""
    agent = SmartMultiMcpAgent(mcp_names, model_name)
    await agent.initialize()
    
    # 返回代理和workbenches列表（为了兼容现有接口）
    # 如果没有MCP服务器，返回空列表
    workbenches = list(agent.manager.workbenches.values()) if agent.manager.workbenches else []
    return agent, workbenches 