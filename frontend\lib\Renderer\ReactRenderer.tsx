'use client'

import React, { useState, useEffect } from 'react'

interface ReactRendererProps {
  code: string
}

// React组件渲染器
const ReactRenderer: React.FC<ReactRendererProps> = ({ code }) => {
  const [Component, setComponent] = useState<React.ComponentType | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    setIsLoading(true)
    setError(null)
    
    try {
      // 创建一个安全的执行环境
      const executeReactCode = (codeString: string) => {
        // 预处理代码：移除import语句，因为我们会提供这些依赖
        let processedCode = codeString
          .replace(/import\s+React[^;]*;?\s*/g, '')
          .replace(/import\s*\{[^}]*\}\s*from\s*['"]react['"][^;]*;?\s*/g, '')
          .replace(/import\s*\*\s*as\s*React\s*from\s*['"]react['"][^;]*;?\s*/g, '')
          .replace(/export\s+default\s+/, 'return ')
        
        // 如果代码不包含return语句，并且看起来是一个组件，添加return
        if (!processedCode.includes('return') && 
            (processedCode.includes('function') || processedCode.includes('const') || processedCode.includes('=>'))) {
          // 提取最后一个组件定义
          const componentMatch = processedCode.match(/((?:function\s+\w+|const\s+\w+\s*=)[^;]*(?:\{[\s\S]*\}|\([^)]*\)\s*=>\s*[\s\S]*))/)
          if (componentMatch) {
            const componentName = componentMatch[0].match(/(?:function\s+(\w+)|const\s+(\w+))/)?.[1] || componentMatch[0].match(/(?:function\s+(\w+)|const\s+(\w+))/)?.[2]
            if (componentName) {
              processedCode = processedCode + `\nreturn ${componentName};`
            }
          }
        }
        
        // 包装代码，提供React环境
        const wrappedCode = `
          const { useState, useEffect, useContext, useReducer, useCallback, useMemo, useRef, React: ReactLib } = arguments[0];
          const React = ReactLib;
          ${processedCode}
        `
        
        // 创建函数并执行
        const func = new Function(wrappedCode)
        const reactDeps = { 
          useState: React.useState, 
          useEffect: React.useEffect,
          useContext: React.useContext,
          useReducer: React.useReducer,
          useCallback: React.useCallback,
          useMemo: React.useMemo,
          useRef: React.useRef,
          React: React
        }
        
        return func(reactDeps)
      }

      // 尝试执行代码
      const ComponentResult = executeReactCode(code)
      
      if (typeof ComponentResult === 'function') {
        // 如果返回的是一个函数组件
        setComponent(() => ComponentResult)
      } else if (React.isValidElement(ComponentResult)) {
        // 如果返回的是JSX元素
        setComponent(() => () => ComponentResult)
      } else {
        // 如果无法识别，尝试一些启发式方法
        createFallbackComponent(code)
      }
      
    } catch (err) {
      console.error('React代码执行错误:', err)
      // 如果动态执行失败，尝试创建一个备用组件
      createFallbackComponent(code)
    }
    
    setIsLoading(false)
  }, [code])

  const createFallbackComponent = (codeString: string) => {
    try {
      // 分析代码中包含的React模式
      const hasReactImport = /import\s+React|from\s+['"]react['"]/.test(codeString)
      const hasUseState = /useState\s*\(/.test(codeString)
      const hasUseEffect = /useEffect\s*\(/.test(codeString)
      const hasJSX = /<[A-Z]\w*|return\s*\(\s*<|=>\s*</.test(codeString)
      const hasClassName = /className\s*=/.test(codeString)
      
      // 如果代码看起来像React组件，尝试创建一个模拟版本
      if (hasReactImport || hasUseState || hasJSX || hasClassName) {
        
        // 计数器组件
        if (codeString.includes('Counter') && hasUseState) {
          const CounterComponent = () => {
            const [count, setCount] = React.useState(0)
            
            return React.createElement('div', {
              className: 'p-6 bg-white rounded-lg shadow-lg max-w-sm mx-auto'
            }, [
              React.createElement('h2', {
                key: 'title',
                className: 'text-2xl font-bold text-center mb-4 text-gray-800'
              }, '计数器组件'),
              React.createElement('div', {
                key: 'content',
                className: 'text-center'
              }, [
                React.createElement('p', {
                  key: 'count',
                  className: 'text-4xl font-mono mb-4 text-blue-600'
                }, count.toString()),
                React.createElement('div', {
                  key: 'buttons',
                  className: 'space-x-2'
                }, [
                  React.createElement('button', {
                    key: 'dec',
                    className: 'px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors',
                    onClick: () => setCount(count - 1)
                  }, '减少'),
                  React.createElement('button', {
                    key: 'reset',
                    className: 'px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors mx-2',
                    onClick: () => setCount(0)
                  }, '重置'),
                  React.createElement('button', {
                    key: 'inc',
                    className: 'px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors',
                    onClick: () => setCount(count + 1)
                  }, '增加')
                ])
              ])
            ])
          }
          
          setComponent(() => CounterComponent)
          setError(null)
          return
        }

        // 表单组件
        if ((codeString.includes('form') || codeString.includes('Form')) && hasUseState) {
          const FormComponent = () => {
            const [formData, setFormData] = React.useState({
              name: '',
              email: '',
              message: ''
            })
            const [submitted, setSubmitted] = React.useState(false)
            
            const handleSubmit = (e: React.FormEvent) => {
              e.preventDefault()
              setSubmitted(true)
              setTimeout(() => setSubmitted(false), 3000)
            }
            
            const handleChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
              setFormData(prev => ({ ...prev, [field]: e.target.value }))
            }
          
            return React.createElement('div', {
              className: 'p-6 bg-white rounded-lg shadow-lg max-w-md mx-auto'
            }, [
              React.createElement('h2', {
                key: 'title',
                className: 'text-2xl font-bold mb-4 text-gray-800'
              }, '联系表单'),
              submitted ? React.createElement('div', {
                key: 'success',
                className: 'p-4 bg-green-100 border border-green-400 text-green-700 rounded mb-4'
              }, '表单提交成功！') : null,
              React.createElement('form', {
                key: 'form',
                onSubmit: handleSubmit,
                className: 'space-y-4'
              }, [
                React.createElement('div', {
                  key: 'name-field'
                }, [
                  React.createElement('label', {
                    key: 'name-label',
                    className: 'block text-sm font-medium text-gray-700 mb-1'
                  }, '姓名'),
                  React.createElement('input', {
                    key: 'name-input',
                    type: 'text',
                    value: formData.name,
                    onChange: handleChange('name'),
                    className: 'w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                    required: true
                  })
                ]),
                React.createElement('div', {
                  key: 'email-field'
                }, [
                  React.createElement('label', {
                    key: 'email-label',
                    className: 'block text-sm font-medium text-gray-700 mb-1'
                  }, '邮箱'),
                  React.createElement('input', {
                    key: 'email-input',
                    type: 'email',
                    value: formData.email,
                    onChange: handleChange('email'),
                    className: 'w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                    required: true
                  })
                ]),
                React.createElement('div', {
                  key: 'message-field'
                }, [
                  React.createElement('label', {
                    key: 'message-label',
                    className: 'block text-sm font-medium text-gray-700 mb-1'
                  }, '留言'),
                  React.createElement('textarea', {
                    key: 'message-input',
                    value: formData.message,
                    onChange: handleChange('message'),
                    rows: 4,
                    className: 'w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                  })
                ]),
                React.createElement('button', {
                  key: 'submit',
                  type: 'submit',
                  className: 'w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition-colors'
                }, '提交')
              ])
            ].filter(Boolean))
          }
          
          setComponent(() => FormComponent)
          setError(null)
          return
        }

        // 待办事项组件
        if (codeString.includes('Todo') || codeString.includes('task')) {
          const TodoComponent = () => {
            const [todos, setTodos] = React.useState([
              { id: 1, text: '学习 React Hooks', completed: false },
              { id: 2, text: '构建用户界面', completed: true },
              { id: 3, text: '添加状态管理', completed: false }
            ])
            const [newTodo, setNewTodo] = React.useState('')
            
            const addTodo = () => {
              if (newTodo.trim()) {
                setTodos(prev => [...prev, {
                  id: Math.max(...prev.map(t => t.id), 0) + 1,
                  text: newTodo,
                  completed: false
                }])
                setNewTodo('')
              }
            }
              
            const toggleTodo = (id: number) => {
              setTodos(prev => prev.map(todo => 
                todo.id === id ? { ...todo, completed: !todo.completed } : todo
              ))
            }
            
            const removeTodo = (id: number) => {
              setTodos(prev => prev.filter(todo => todo.id !== id))
            }
          
            return React.createElement('div', {
              className: 'p-6 bg-white rounded-lg shadow-lg max-w-md mx-auto'
            }, [
              React.createElement('h3', {
                key: 'title',
                className: 'text-xl font-bold mb-4 text-gray-800'
              }, '待办事项列表'),
              React.createElement('div', {
                key: 'input-area',
                className: 'flex gap-2 mb-4'
              }, [
                React.createElement('input', {
                  key: 'input',
                  type: 'text',
                  value: newTodo,
                  onChange: (e: React.ChangeEvent<HTMLInputElement>) => setNewTodo(e.target.value),
                  placeholder: '添加新任务...',
                  className: 'flex-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500',
                  onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => e.key === 'Enter' && addTodo()
                }),
                React.createElement('button', {
                  key: 'add-btn',
                  onClick: addTodo,
                  className: 'px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors'
                }, '添加')
              ]),
              React.createElement('ul', {
                key: 'todo-list',
                className: 'space-y-2'
              }, todos.map(todo => 
                React.createElement('li', {
                  key: todo.id,
                  className: 'flex items-center gap-2 p-3 bg-gray-50 rounded-md'
                }, [
                  React.createElement('input', {
                    key: 'checkbox',
                    type: 'checkbox',
                    checked: todo.completed,
                    onChange: () => toggleTodo(todo.id),
                    className: 'w-4 h-4 text-blue-600'
                  }),
                  React.createElement('span', {
                    key: 'text',
                    className: `flex-1 ${todo.completed ? 'line-through text-gray-500' : 'text-gray-800'}`
                  }, todo.text),
                  React.createElement('button', {
                    key: 'delete',
                    onClick: () => removeTodo(todo.id),
                    className: 'px-2 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors'
                  }, '删除')
                ])
              ))
            ])
          }
          
          setComponent(() => TodoComponent)
          setError(null)
          return
        }

        // 通用React组件
        const GenericComponent = () => {
          return React.createElement('div', {
            className: 'p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto'
          }, [
            React.createElement('h2', {
              key: 'title',
              className: 'text-2xl font-bold mb-4 text-gray-800'
            }, 'React 组件预览'),
            React.createElement('div', {
              key: 'content',
              className: 'prose max-w-none'
            }, [
              React.createElement('p', {
                key: 'description',
                className: 'text-gray-600 mb-4'
              }, '这是一个React组件的预览。由于安全限制，无法直接执行自定义代码。'),
              React.createElement('div', {
                key: 'code-sample',
                className: 'bg-gray-100 p-4 rounded-lg'
              }, [
                React.createElement('h3', {
                  key: 'code-title',
                  className: 'text-lg font-semibold mb-2'
                }, '组件代码结构:'),
                React.createElement('ul', {
                  key: 'features',
                  className: 'list-disc list-inside space-y-1 text-sm text-gray-700'
                }, [
                  hasReactImport && React.createElement('li', { key: 'import' }, '✓ 包含React导入'),
                  hasUseState && React.createElement('li', { key: 'useState' }, '✓ 使用useState Hook'),
                  hasUseEffect && React.createElement('li', { key: 'useEffect' }, '✓ 使用useEffect Hook'),
                  hasJSX && React.createElement('li', { key: 'jsx' }, '✓ 包含JSX语法'),
                  hasClassName && React.createElement('li', { key: 'className' }, '✓ 使用className属性')
                ].filter(Boolean))
              ])
            ])
          ])
        }
        
        setComponent(() => GenericComponent)
        setError(null)
      } else {
        setError('无法识别的React组件格式')
      }
    } catch (err) {
      setError(`代码解析错误: ${err instanceof Error ? err.message : '未知错误'}`)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-gray-600">正在渲染React组件...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-lg font-semibold text-red-800 mb-2">组件渲染错误</h3>
        <p className="text-red-600 text-sm">{error}</p>
        <details className="mt-3">
          <summary className="text-red-700 cursor-pointer text-sm">查看原始代码</summary>
          <pre className="mt-2 text-xs bg-red-100 p-2 rounded overflow-auto max-h-32">
            {code}
          </pre>
        </details>
      </div>
    )
  }

  if (!Component) {
    return (
      <div className="p-6 bg-gray-50 border border-gray-200 rounded-lg">
        <p className="text-gray-600">无法渲染此React组件</p>
      </div>
    )
  }

  try {
    return (
      <div className="h-full overflow-auto bg-gray-50">
        <Component />
      </div>
    )
  } catch (renderError) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-lg font-semibold text-red-800 mb-2">组件运行时错误</h3>
        <p className="text-red-600 text-sm">
          {renderError instanceof Error ? renderError.message : '组件渲染时发生未知错误'}
        </p>
      </div>
    )
  }
}

export default ReactRenderer 