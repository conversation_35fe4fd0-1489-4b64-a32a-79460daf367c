{"mcpServers": {"amap-maps": {"command": "npx", "args": ["-y", "@amap/amap-maps-mcp-server"], "env": {"AMAP_MAPS_API_KEY": "441e03f8b7c8788159b71ee158786aac"}}, "airbnb": {"command": "npx", "args": ["-y", "@openbnb/mcp-server-airbnb"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest", "--headless"]}, "edgeone-pages-mcp": {"command": "npx", "args": ["-y", "mcprouter"], "env": {"SERVER_KEY": "7h073nmbbtb56t"}}, "github": {"command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "ghcr.io/github/github-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "mermaid": {"command": "npx", "args": ["-y", "@peng-shawn/mermaid-mcp-server"]}, "302ai-sandbox-mcp": {"command": "npx", "args": ["-y", "@302ai/sandbox-mcp"]}, "quickchart-server": {"command": "npx", "args": ["-y", "@gongrzhe/quickchart-mcp-server"]}, "howtocook-mcp": {"command": "npx", "args": ["-y", "howtocook-mcp"]}, "exa": {"command": "npx", "args": ["-y", "exa-mcp-server"], "env": {"EXA_API_KEY": "5389dcc5-ca30-4f01-a474-cdc24857bc05"}}}}