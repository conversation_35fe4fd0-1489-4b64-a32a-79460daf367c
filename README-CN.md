# Zenus: 下一代AI智能体

[English](README.md) | **中文**

Zenus 是一个多智能体聊天机器人，可以帮助您完成各种任务。

## 目录

- [特性](#特性)
- [快速开始](#快速开始)
  - [前置条件](#前置条件)
  - [启动应用](#启动应用)
- [测试](#测试mcp服务)
- [Docker部署](#docker部署)
- [贡献](#贡献)
- [许可证](#许可证)

## 特性

- 🤖 **多智能体架构**: 规划器 + 智能体执行模型
- 🧠 **智能任务规划**: AI驱动的任务分析和分配  
- 🔧 **MCP集成**: 支持多个MCP服务器（GitHub、高德地图、Airbnb、Playwright）
- 📱 **响应式设计**: 在桌面端和移动端无缝运行
- ⚡ **实时流式传输**: 支持流式响应的实时聊天
- 🎨 **组件展示**: HTML和React组件的交互式预览


## 快速开始

### 前置条件

1. 前端
> 安装 [npm](https://nodejs.org)

```bash
# 安装依赖
cd frontend
npm install

# 配置后端URL
cp .env.example .env.local
```

2. 后端
> 安装 [miniconda](https://www.anaconda.com/docs/getting-started/miniconda/install)
```bash
# 使用 miniconda 创建一个独立的python环境
conda create -n zenus python==3.11 -y
conda activate zenus

# 安装依赖
cd backend
pip install -r requirements.txt
```

3. 配置后端URL:
```bash
cd frontend
cp .env.example .env.local
```

### 启动应用
使用提供的启动脚本一键启动前后端服务：
```bash
make dev
```

## Docker部署

```bash
docker build -t zenus:v1.0.0 . 
docker run -p 3000:3000 -p 8000:8000 zenus:v1.0.0
```

## 贡献

欢迎提交Pull Request和Issue来帮助改进Zenus！

## 许可证

MIT License 