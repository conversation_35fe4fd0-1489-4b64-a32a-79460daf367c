import asyncio
from agent.llm_chat import LLMChat
from config.models import ChatRequest

async def main():
    # 构造一个测试请求
    req = ChatRequest(
        message="查询一下北京一周的天气",
        mcp_servers=["amap-maps"],
        model="deepseek-r1-0528"  # 这就是 model_name
    )
    
    print("--- 测试 chat_stream ---")
    async for chunk in LLMChat.chat_stream(req.message, req.mcp_servers, req.model):
        print(chunk, end="")

if __name__ == "__main__":
    asyncio.run(main())

# cd backend && python -m playground.test_llm_chat
