import asyncio
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.tools.mcp import StdioServerParams, StreamableHttpServerParams, mcp_server_tools
from dotenv import load_dotenv, find_dotenv
import json
from pprint import pprint

# find_dotenv 会沿着父目录一直找指定文件

env_path = find_dotenv(".env.local", raise_error_if_not_found=True)
load_dotenv(env_path, override=True)

def get_mcp_server_params(name: str, transport: str):
    if transport == "stdio":
        with open("config/mcp-stdio.json", "r", encoding="utf-8") as f:
            cfg = json.load(f)["mcpServers"][name]
        return StdioServerParams(**cfg)
    elif transport == "streamable-http":
        with open("config/mcp-streamable-http.json", "r", encoding="utf-8") as f:
            cfg = json.load(f)["mcpServers"][name]
        return StreamableHttpServerParams(**cfg)
    else:
        raise ValueError(f"Invalid transport: {transport}")

async def main(model_name: str, mcp_name: str, task: str, transport: str) -> None:
    model_client = OpenAIChatCompletionClient(model=model_name)
    server_params = get_mcp_server_params(mcp_name, transport)
    tools = await mcp_server_tools(server_params)
    tool_list = [
        {
            "tool_name": tool.name,
            "tool_description": tool.description,
        }
        for tool in tools
    ]

    print(json.dumps(tool_list, ensure_ascii=False, indent=2))


    agent = AssistantAgent(
        name="assistant",
        model_client=model_client,
        system_message=f"You are a helpful assistant that can use the {mcp_name} MCP server to help the user.",
        tools=tools,
        reflect_on_tool_use=True,
        model_client_stream=True,
    )
    await Console(agent.run_stream(task=task))


# asyncio.run(main("gpt-4o-mini", "github", "Help me create a new repository named zeus-ai", "stdio"))
# asyncio.run(main("gpt-4o-mini", "quickchart-server", "帮我画一个折线图", "stdio"))
# asyncio.run(main("gpt-4o-mini", "fetch", "https://github.com/", "streamable-http"))
# asyncio.run(main("gpt-4o-mini", "airbnb", "我想要纽约一晚$300的酒店"))
# asyncio.run(main("gpt-4o-mini", "mermaid", "人工智能发展史流程图"))
# asyncio.run(main("gpt-4o-mini", "playwright", "帮我打开百度"))
asyncio.run(main("gpt-4o-mini", "exa", "分析本月问界M7各大区线销售情况", "stdio"))

# cd backend && python playground/test_mcptools.py