{"name": "mcp-autogen", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@e2b/code-interpreter": "^1.5.1", "@gongrzhe/quickchart-mcp-server": "^1.0.6", "@handsontable/react": "^15.3.0", "@openbnb/mcp-server-airbnb": "^0.1.1", "@playwright/mcp": "^0.0.28", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@smithery/cli": "^1.2.11", "@types/react-syntax-highlighter": "^15.5.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "exa-mcp-server": "^0.3.10", "handsontable": "^15.3.0", "howtocook-mcp": "^0.0.8", "html2pdf.js": "^0.10.3", "lucide-react": "^0.511.0", "mermaid": "^11.7.0", "next": "15.3.3", "pptxgenjs": "^4.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.2", "typescript": "^5"}}