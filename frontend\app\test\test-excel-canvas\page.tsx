'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { HotTable } from '@handsontable/react'
import * as XLSX from 'xlsx'
import 'handsontable/dist/handsontable.full.min.css'
import ChatInterface from '@/components/chat-interface'
import Sidebar from '@/components/Sidebar'
import { Button } from '@/components/ui/button'
import { Download, X, Maximize, FileSpreadsheet, Minimize } from 'lucide-react'

interface ExcelData {
  data: (string | number)[][]
  headers: string[]
  sheetName: string
}

export default function TestExcelCanvas() {
  const [excelData, setExcelData] = useState<ExcelData | null>(null)
  const [selectedSheet, setSelectedSheet] = useState<string>('')
  const [allSheets, setAllSheets] = useState<{ [key: string]: ExcelData }>({})
  const [isLoading, setIsLoading] = useState(false)
  const [fileName, setFileName] = useState<string>('')
  const [isFullScreen, setIsFullScreen] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Sidebar state
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true)

  // Sidebar toggle function
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  // 处理文件上传
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setFileName(file.name)
    setIsLoading(true)
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        
        const sheets: { [key: string]: ExcelData } = {}
        let firstSheetName = ''
        
        workbook.SheetNames.forEach((sheetName, index) => {
          const worksheet = workbook.Sheets[sheetName]
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
            header: 1,
            defval: '',
            raw: false
          }) as (string | number)[][]
          
          const data = jsonData
          // 生成默认的列标题
          const maxCols = Math.max(...data.map(row => row.length))
          const headers = Array.from({length: maxCols}, (_, i) => String.fromCharCode(65 + i))
          
          sheets[sheetName] = {
            data,
            headers,
            sheetName
          }
          
          if (index === 0) {
            firstSheetName = sheetName
          }
        })
        
        setAllSheets(sheets)
        setSelectedSheet(firstSheetName)
        setExcelData(sheets[firstSheetName])
      } catch (error) {
        console.error('Error reading Excel file:', error)
        alert('无法读取 Excel 文件，请确保文件格式正确')
      } finally {
        setIsLoading(false)
      }
    }
    
    reader.readAsArrayBuffer(file)
  }, [])

  // 切换工作表
  const handleSheetChange = (sheetName: string) => {
    setSelectedSheet(sheetName)
    setExcelData(allSheets[sheetName])
  }

  // 生成示例数据
  const generateSampleData = () => {
    const sampleData = {
      data: [
        ['Year-over-Year Sales Comparison (2024-2025)', '', '', '', ''],
        ['Region', '2024 Comparable ($K)', '2025 Partial ($K)', 'YoY Growth ($K)', 'YoY %'],
        ['Central', 3842, 2831, -1011, '-26.3%'],
        ['East', 3169, 4158, 989, '31.2%'],
        ['North', 4185, 3825, -360, '-8.6%'],
        ['South', 4282, 3168, -1114, '-26.0%'],
        ['West', 3415, 3458, 43, '1.3%'],
        ['', '', '', '', ''],
        ['TOTAL', 18893, 17440, -1453, '-7.7%']
      ],
      headers: ['A', 'B', 'C', 'D', 'E'],
      sheetName: 'YoY Comparison'
    }
    
    setFileName('示例数据.xlsx')
    setAllSheets({ 
      'YoY Comparison': sampleData,
      'Quarterly Sales Data': sampleData,
      'Sales Analysis': sampleData,
      'Quarterly Trends': sampleData,
      'Documentation': sampleData
    })
    setSelectedSheet('YoY Comparison')
    setExcelData(sampleData)
  }

  // 全屏切换
  const toggleFullScreen = useCallback(async () => {
    try {
      if (!isFullScreen) {
        // 进入全屏
        const element = document.documentElement
        if (element.requestFullscreen) {
          await element.requestFullscreen()
        } else if ((element as Document['documentElement'] & { webkitRequestFullscreen?: () => Promise<void> }).webkitRequestFullscreen) {
          await (element as Document['documentElement'] & { webkitRequestFullscreen: () => Promise<void> }).webkitRequestFullscreen()
        } else if ((element as Document['documentElement'] & { msRequestFullscreen?: () => Promise<void> }).msRequestFullscreen) {
          await (element as Document['documentElement'] & { msRequestFullscreen: () => Promise<void> }).msRequestFullscreen()
        }
        setIsFullScreen(true)
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          await document.exitFullscreen()
        } else if ((document as Document & { webkitExitFullscreen?: () => Promise<void> }).webkitExitFullscreen) {
          await (document as Document & { webkitExitFullscreen: () => Promise<void> }).webkitExitFullscreen()
        } else if ((document as Document & { msExitFullscreen?: () => Promise<void> }).msExitFullscreen) {
          await (document as Document & { msExitFullscreen: () => Promise<void> }).msExitFullscreen()
        }
        setIsFullScreen(false)
      }
    } catch (error) {
      console.log('全屏切换失败:', error)
      // 降级到简单的布局切换
      setIsFullScreen(!isFullScreen)
    }
  }, [isFullScreen])

  // 监听浏览器全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = 
        document.fullscreenElement ||
        (document as Document & { webkitFullscreenElement?: Element }).webkitFullscreenElement ||
        (document as Document & { msFullscreenElement?: Element }).msFullscreenElement
      
      setIsFullScreen(!!isCurrentlyFullscreen)
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullScreen) {
        toggleFullScreen()
      }
      if (event.key === 'F11') {
        event.preventDefault()
        toggleFullScreen()
      }
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('msfullscreenchange', handleFullscreenChange)
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.removeEventListener('msfullscreenchange', handleFullscreenChange)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isFullScreen, toggleFullScreen])

  // 下载Excel文件
  const downloadExcel = () => {
    if (!excelData || Object.keys(allSheets).length === 0) return
    
    const wb = XLSX.utils.book_new()
    
    Object.entries(allSheets).forEach(([sheetName, sheetData]) => {
      const ws = XLSX.utils.aoa_to_sheet(sheetData.data)
      XLSX.utils.book_append_sheet(wb, ws, sheetName)
    })
    
    XLSX.writeFile(wb, fileName || 'exported_data.xlsx')
  }

  // 关闭页面
  const handleClose = () => {
    // 可以添加确认对话框
    if (confirm('确定要关闭Excel预览吗？')) {
      window.close()
    }
  }

  return (
    <div className={`h-screen bg-gray-50 flex transition-all duration-500 ease-in-out ${
      isFullScreen ? 'bg-white' : ''
    }`}>
      {!isFullScreen && (
        <>
          {/* 左侧边栏 */}
          <div className="transition-all duration-500 ease-in-out">
            <Sidebar isCollapsed={sidebarCollapsed} onToggle={toggleSidebar} />
          </div>

          {/* 中间聊天界面 */}
          <div className={`bg-white border-r border-gray-200 transition-all duration-500 ease-in-out ${
            sidebarCollapsed ? 'flex-1' : 'w-1/3'
          }`}>
            <ChatInterface />
          </div>
        </>
      )}

      {/* 右侧Excel预览区域 */}
      <div className={`bg-white flex flex-col transition-all duration-500 ease-in-out ${
        isFullScreen ? 'w-full h-full' : sidebarCollapsed ? 'w-1/2' : 'w-2/3'
      }`}>
        {/* Excel Canvas头部工具栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-gray-800 flex items-center">
              <FileSpreadsheet className="w-5 h-5 mr-2" />
              Excel Canvas
            </h1>
            {fileName && (
              <span className="text-sm text-gray-600">
                {fileName}
              </span>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {/* 全屏按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullScreen}
              className="flex items-center space-x-1"
              disabled={!excelData}
              title={isFullScreen ? "退出全屏" : "进入全屏"}
            >
              {isFullScreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
            </Button>

            {/* 下载按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={downloadExcel}
              className="flex items-center space-x-1"
              disabled={!excelData}
            >
              <Download className="w-4 h-4" />
            </Button>

            {/* 关闭按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleClose}
              className="flex items-center space-x-1 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* 文件上传控制区域 */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-center space-x-4">
            <input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileUpload}
              className="hidden"
            />
            <Button
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? '加载中...' : '上传 Excel 文件'}
            </Button>
            <Button
              variant="outline"
              onClick={generateSampleData}
            >
              生成示例数据
            </Button>
            <span className="text-sm text-gray-500">
              支持 .xlsx, .xls, .csv 格式
            </span>
          </div>
        </div>

        {/* Excel预览区域 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Excel 表格区域 */}
          <div className="flex-1 overflow-hidden bg-white">
            {excelData ? (
              <div className="h-full excel-container">
                <style jsx global>{`
                  .excel-container .handsontable {
                    font-family: 'Segoe UI', Arial, sans-serif !important;
                    font-size: 11px !important;
                    color: #000 !important;
                  }
                  
                  .excel-container .handsontable td {
                    border: 1px solid #d4d4d4 !important;
                    background: white !important;
                    vertical-align: middle !important;
                  }
                  
                  .excel-container .handsontable th {
                    background: #f2f2f2 !important;
                    border: 1px solid #d4d4d4 !important;
                    font-weight: normal !important;
                    color: #000 !important;
                    text-align: center !important;
                    font-size: 11px !important;
                  }
                  
                  .excel-container .handsontable .htCenter {
                    text-align: center !important;
                  }
                  
                  .excel-container .handsontable .htLeft {
                    text-align: left !important;
                    padding-left: 6px !important;
                  }
                  
                  .excel-container .handsontable .htRight {
                    text-align: right !important;
                    padding-right: 6px !important;
                  }
                  
                  .excel-container .handsontable .current {
                    border: 1px solid #007e4e !important;
                  }
                  
                  .excel-container .handsontable .area {
                    background: rgba(0, 126, 78, 0.1) !important;
                  }
                  
                  .excel-container .handsontable .total-row {
                    background: #f2f2f2 !important;
                    font-weight: bold !important;
                    border-top: 2px solid #000 !important;
                  }
                `}</style>

                <HotTable
                  data={excelData.data}
                  colHeaders={['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']}
                  rowHeaders={true}
                  width="100%"
                  height="100%"
                  licenseKey="non-commercial-and-evaluation"
                  settings={{
                    // 基础设置
                    stretchH: 'all',
                    manualColumnResize: true,
                    manualRowResize: true,
                    colWidths: [120, 150, 150, 150, 120, 80, 80, 80, 80, 80],
                    rowHeights: 20,
                    
                    // 外观设置
                    className: 'htCenter htMiddle',
                    
                    // 功能设置
                    copyPaste: true,
                    fillHandle: {
                      direction: 'vertical',
                      autoInsertRow: false
                    },
                    contextMenu: true,
                    
                    // 单元格设置
                    cells: function(row: number, col: number) {
                      const cellProperties: Record<string, unknown> = {}
                      const cellValue = excelData.data[row]?.[col]
                      
                      // TOTAL 行样式
                      if (cellValue === 'TOTAL' || (row > 1 && excelData.data[row]?.[0] === 'TOTAL')) {
                        cellProperties.className = 'total-row htCenter'
                        if (col === 0) {
                          cellProperties.className = 'total-row htLeft'
                        }
                      }
                      // 数值列右对齐
                      else if (col > 0 && cellValue && !isNaN(Number(cellValue))) {
                        cellProperties.className = 'htRight'
                      }
                      // 百分比列
                      else if (typeof cellValue === 'string' && cellValue.includes('%')) {
                        cellProperties.className = 'htRight'
                      }
                      // 空行处理
                      else if (!cellValue || cellValue === '') {
                        cellProperties.className = 'htCenter'
                      }
                      // 文本列左对齐
                      else {
                        cellProperties.className = 'htLeft'
                      }
                      
                      return cellProperties
                    },
                    
                    // 合并单元格（标题行）
                    mergeCells: [
                      {row: 0, col: 0, rowspan: 1, colspan: 5}
                    ]
                  }}
                />
              </div>
            ) : (
              <div className="h-full flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg m-4">
                <div className="text-center text-gray-500">
                  <div className="text-4xl mb-4">📊</div>
                  <p className="text-lg font-medium">Excel 预览画布</p>
                  <p className="text-sm">上传 Excel 文件或生成示例数据以开始预览</p>
                </div>
              </div>
            )}
          </div>

          {/* 工作表标签栏 - 移动到底部 */}
          <div className="bg-[#ebecec] border-t border-gray-300 h-8">
            <div className="flex items-center h-full">
              {/* 工作表导航按钮 */}
              <div className="flex border-r border-gray-300 h-full">
                <button className="px-2 text-black hover:text-gray-600 border-r border-gray-300 h-full flex items-center">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                    <path d="M8.5 2.5L5 6l3.5 3.5"/>
                  </svg>
                </button>
                <button className="px-2 text-black hover:text-gray-600 border-r border-gray-300 h-full flex items-center">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                    <path d="M4.5 2.5L8 6l-3.5 3.5"/>
                  </svg>
                </button>
                <button className="px-2 text-black hover:text-gray-600 border-r border-gray-300 h-full flex items-center">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                    <path d="M3.5 2.5L7 6l-3.5 3.5M7.5 2.5L11 6l-3.5 3.5"/>
                  </svg>
                </button>
              </div>
              
              {/* 工作表标签 */}
              <div className="flex-1 flex h-full">
                {Object.keys(allSheets).map((sheetName) => (
                  <button
                    key={sheetName}
                    onClick={() => handleSheetChange(sheetName)}
                    className={`px-4 text-xs border-r border-gray-300 whitespace-nowrap h-full flex items-center ${
                      selectedSheet === sheetName
                        ? 'bg-white text-[#007e4e] font-bold border-b-2 border-b-[#007e4e]'
                        : 'bg-[#ebecec] text-black hover:bg-gray-200'
                    }`}
                  >
                    {sheetName}
                  </button>
                ))}
                <button className="px-2 text-black hover:text-gray-600 h-full flex items-center">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                    <path d="M6 3v6M3 6h6"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Excel 状态栏 */}
          <div className="bg-[#ebecec] border-t border-gray-300 px-4 h-8">
            <div className="flex items-center justify-between text-xs text-black h-full">
              <div className="flex items-center space-x-4">
                <span>就绪</span>
                {excelData && (
                  <>
                    <span>行数: {excelData.data.length}</span>
                    <span>列数: {excelData.headers.length}</span>
                  </>
                )}
              </div>
              <div className="flex items-center space-x-4">
                <span>工作表 {Object.keys(allSheets).indexOf(selectedSheet) + 1} / {Object.keys(allSheets).length}</span>
                <span>平均值: ; 求和: 0</span>
                <span>只读</span>
                <div className="flex items-center space-x-1">
                  <button className="p-1 hover:bg-gray-200 rounded">
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                      <path d="M2 2h8v8H2z"/>
                    </svg>
                  </button>
                  <span>100%</span>
                  <button className="p-1 hover:bg-gray-200 rounded">
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
                      <path d="M6 2v8M2 6h8"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
