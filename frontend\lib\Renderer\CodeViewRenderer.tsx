import React, { useEffect } from 'react'
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter'
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'

interface CodeViewRendererProps {
  code: string
  language: string
  showLineNumbers?: boolean
  height?: string
}

const CodeViewRenderer: React.FC<CodeViewRendererProps> = ({ 
  code, 
  language, 
  showLineNumbers = true,
  height = '100%'
}) => {
  // 自定义样式来覆盖行号颜色
  const customLineNumberStyle = {
    color: '#858585 !important',
    paddingRight: '8px',
    borderRight: '1px solid #3e3e3e',
    marginRight: '8px',
    minWidth: '2em',
    userSelect: 'none' as const,
    fontSize: '14px'
  }

  // 自定义主题，基于 vscDarkPlus 但覆盖行号颜色
  const customTheme = {
    ...vscDarkPlus,
    'pre[class*="language-"]': {
      ...vscDarkPlus['pre[class*="language-"]'],
      background: '#1e1e1e'
    },
    '.line-numbers .line-numbers-rows > span:before': {
      color: '#858585 !important'
    }
  }

  // 添加全局样式来确保行号颜色
  useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      .react-syntax-highlighter-line-number {
        color: #858585 !important;
      }
      .token.linenumber {
        color: #858585 !important;
      }
      .line-numbers-rows > span:before {
        color: #858585 !important;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])

  return (
    <div className="h-full overflow-auto">
      <SyntaxHighlighter
        language={language}
        style={customTheme}
        customStyle={{
          margin: 0,
          padding: '6px',
          background: '#1e1e1e',
          fontSize: '14px',
          lineHeight: '1.5',
          height: height,
          overflow: 'auto'
        }}
        showLineNumbers={showLineNumbers}
        wrapLines={true}
        lineNumberStyle={customLineNumberStyle}
      >
        {code}
      </SyntaxHighlighter>
    </div>
  )
}

export default CodeViewRenderer 