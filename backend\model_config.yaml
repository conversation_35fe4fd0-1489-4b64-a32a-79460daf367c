# 模型配置文件
# 在这里可以配置所有可用的模型，包括模型名称、显示名称等 

openai:
  base_url: https://api.openai.com/v1
  # base_url: https://api.tu-zi.com/v1
  # api_key: sk-wyEhWuKsfARrFkATfhFPf2VfzJZxBP2MUTZb86VoG9whTlkQ
  api_key: ********************************************************************************************************************************************************************
  models:
    - model_name: gpt-4.1
      model_display_name: OpenAI GPT-4.1 Turbo
    - model_name: gpt-4o-mini
      model_display_name: OpenAI GPT-4o-mini

anthropic:
  # base_url: https://api.anthropic.com
  # api_key: ************************************************************************************************************
  base_url: https://api.tu-zi.com
  api_key: sk-wyEhWuKsfARrFkATfhFPf2VfzJZxBP2MUTZb86VoG9whTlkQ
  models:
    - model_name: claude-sonnet-4-20250514
      model_display_name: Claude 4 Sonnet
    - model_name: claude-opus-4-20250514
      model_display_name: Claude 4 Opus

google:
  base_url: https://api.tu-zi.com/v1
  api_key: sk-wyEhWuKsfARrFkATfhFPf2VfzJZxBP2MUTZb86VoG9whTlkQ
  models:
    - model_name: gemini-2.5-pro-preview-05-06
      model_display_name: Gemini 2.5 Pro

deepseek:
  base_url: https://api.tu-zi.com/v1
  api_key: sk-wyEhWuKsfARrFkATfhFPf2VfzJZxBP2MUTZb86VoG9whTlkQ
  models:
    - model_name: deepseek-r1-0528
      model_display_name: DeepSeek-R1-0528
    - model_name: deepseek-v3-0324
      model_display_name: DeepSeek-V3-0324