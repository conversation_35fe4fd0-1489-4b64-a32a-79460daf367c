export interface ArtifactData {
  id: string
  type: 'html' | 'javascript' | 'python' | 'react' | 'markdown' | 'text' | 'json' | 'css' | 'svg'
  title?: string
  content: string
  language?: string
}

export interface ParsedMessage {
  content: string // 去除artifacts标签后的内容
  artifacts: ArtifactData[]
}

/**
 * 移除内容中的 <think> 标签，避免干扰HTML检测和React渲染
 */
export function removeThinkTags(content: string): string {
  return content.replace(/<think[^>]*>[\s\S]*?<\/think>/gi, '').trim()
}

/**
 * 增强的React组件检测模式
 * 支持更多React库和模式
 */
const REACT_PATTERNS = {
  // 基础React导入
  imports: [
    /import\s+React/i,
    /from\s+['"]react['"]/i,
    /import\s*\{\s*[^}]*\}\s*from\s+['"]react['"]/i,
    /import\s+\*\s+as\s+React\s+from\s+['"]react['"]/i,
  ],
  
  // React Hooks
  hooks: [
    /useState\s*\(/,
    /useEffect\s*\(/,
    /useContext\s*\(/,
    /useReducer\s*\(/,
    /useCallback\s*\(/,
    /useMemo\s*\(/,
    /useRef\s*\(/,
    /useImperativeHandle\s*\(/,
    /useLayoutEffect\s*\(/,
    /useDebugValue\s*\(/,
    /use[A-Z]\w*\s*\(/,  // 自定义hooks
  ],
  
  // 组件定义模式
  components: [
    /function\s+[A-Z]\w*\s*\([^)]*\)\s*\{/,  // 函数组件
    /const\s+[A-Z]\w*\s*=\s*\([^)]*\)\s*=>\s*\{/,  // 箭头函数组件
    /const\s+[A-Z]\w*:\s*React\.FC/,  // TypeScript函数组件
    /const\s+[A-Z]\w*:\s*FunctionComponent/,  // TypeScript函数组件
    /class\s+[A-Z]\w*\s+extends\s+(React\.)?Component/,  // 类组件
    /export\s+default\s+function\s+[A-Z]\w*/,  // 导出函数组件
    /export\s+const\s+[A-Z]\w*\s*=/,  // 导出常量组件
  ],
  
  // JSX模式
  jsx: [
    /return\s*\(\s*</,
    /return\s+</,
    /=>\s*</,
    /=>\s*\(\s*</,
    /<[A-Z]\w*[^>]*>/,  // 自定义组件JSX
    /<\w+[^>]*\s+\w+\s*=\s*\{[^}]*\}/,  // 带props的JSX
    /className\s*=/,  // React特有的className
    /onClick\s*=/,  // React事件处理
    /onChange\s*=/,
    /onSubmit\s*=/,
  ],
  
  // 常用React库导入
  libraries: [
    /from\s+['"]react-router/i,
    /from\s+['"]react-dom/i,
    /from\s+['"]@mui\/material/i,
    /from\s+['"]antd/i,
    /from\s+['"]@ant-design/i,
    /from\s+['"]react-bootstrap/i,
    /from\s+['"]semantic-ui-react/i,
    /from\s+['"]@chakra-ui/i,
    /from\s+['"]react-native/i,
    /from\s+['"]next/i,
    /from\s+['"]@next/i,
    /from\s+['"]styled-components/i,
    /from\s+['"]emotion/i,
    /from\s+['"]@emotion/i,
    /from\s+['"]framer-motion/i,
    /from\s+['"]react-spring/i,
    /from\s+['"]react-query/i,
    /from\s+['"]@tanstack\/react-query/i,
    /from\s+['"]zustand/i,
    /from\s+['"]redux/i,
    /from\s+['"]react-redux/i,
    /from\s+['"]recoil/i,
    /from\s+['"]formik/i,
    /from\s+['"]react-hook-form/i,
    /from\s+['"]react-select/i,
    /from\s+['"]react-datepicker/i,
    /from\s+['"]react-table/i,
    /from\s+['"]recharts/i,
    /from\s+['"]react-chartjs/i,
    /from\s+['"]react-icons/i,
    /from\s+['"]lucide-react/i,
    /from\s+['"]heroicons/i,
  ],
  
  // TypeScript模式
  typescript: [
    /:\s*React\.FC/,
    /:\s*FunctionComponent/,
    /:\s*ReactNode/,
    /:\s*ReactElement/,
    /:\s*JSX\.Element/,
    /interface\s+\w*Props/,
    /type\s+\w*Props\s*=/,
    /<[A-Z]\w*<[^>]*>>/,  // 泛型组件
  ],
  
  // Next.js特有模式
  nextjs: [
    /from\s+['"]next\/image['"]/i,
    /from\s+['"]next\/link['"]/i,
    /from\s+['"]next\/router['"]/i,
    /from\s+['"]next\/head['"]/i,
    /getStaticProps/,
    /getServerSideProps/,
    /getStaticPaths/,
    /useRouter\s*\(/,
  ],
  
  // 导出模式
  exports: [
    /export\s+default\s+\w+/,
    /export\s*\{\s*\w+[^}]*\}/,
    /module\.exports\s*=/,
  ]
}

/**
 * 检查代码是否是React组件
 * 使用增强的模式匹配
 */
function isReactComponent(content: string): boolean {
  // 计算匹配的模式权重
  let score = 0
  
  // 检查各种模式
  Object.entries(REACT_PATTERNS).forEach(([category, patterns]) => {
    const matchCount = patterns.filter(pattern => pattern.test(content)).length
    
    // 不同类别的权重不同
    switch (category) {
      case 'imports':
        score += matchCount * 3  // 导入权重较高
        break
      case 'hooks':
        score += matchCount * 4  // Hooks权重最高，因为是React特有的
        break
      case 'components':
        score += matchCount * 3  // 组件定义权重较高
        break
      case 'jsx':
        score += matchCount * 2  // JSX权重中等
        break
      case 'libraries':
        score += matchCount * 2  // React库权重中等
        break
      case 'typescript':
        score += matchCount * 1  // TypeScript权重较低
        break
      case 'nextjs':
        score += matchCount * 2  // Next.js权重中等
        break
      case 'exports':
        score += matchCount * 1  // 导出权重较低
        break
    }
  })
  
  // 如果分数超过阈值，认为是React组件
  return score >= 3
}

/**
 * 从React组件代码中提取组件名称
 * 支持多种命名模式
 */
function extractComponentName(content: string): string | null {
  const patterns = [
    // 函数组件
    /export\s+default\s+function\s+([A-Z]\w*)/,
    /function\s+([A-Z]\w*)\s*\(/,
    
    // 箭头函数组件
    /const\s+([A-Z]\w*)\s*=\s*\(/,
    /export\s+const\s+([A-Z]\w*)\s*=/,
    
    // 类组件
    /class\s+([A-Z]\w*)\s+extends/,
    /export\s+default\s+class\s+([A-Z]\w*)/,
    
    // 直接导出
    /export\s+default\s+([A-Z]\w*)/,
    
    // TypeScript接口中的组件名
    /interface\s+([A-Z]\w*)Props/,
    /type\s+([A-Z]\w*)Props\s*=/,
  ]
  
  for (const pattern of patterns) {
    const match = content.match(pattern)
    if (match && match[1]) {
      return match[1]
    }
  }
  
  return null
}

/**
 * 解析消息中的 react-components
 * 支持格式：
 * <react-components>
 *   <component title="示例组件">
 *     React组件代码
 *   </component>
 * </react-components>
 * 
 * 或者简化格式：
 * <react-components title="示例组件">
 *   React组件代码
 * </react-components>
 */
export function parseReactComponents(content: string): ParsedMessage {
  const artifacts: ArtifactData[] = []
  // 预处理：移除 <think> 标签
  let cleanContent = removeThinkTags(content)

  // 匹配 <react-components>...</react-components> 包装的内容
  const reactComponentsBlockRegex = /<react-components[^>]*>([\s\S]*?)<\/react-components>/gi
  const reactComponentsBlocks = content.match(reactComponentsBlockRegex)

  if (reactComponentsBlocks) {
    reactComponentsBlocks.forEach((block, blockIndex) => {
      // 移除外层的 <react-components> 标签
      const innerContent = block.replace(/<\/?react-components[^>]*>/gi, '')
      
      // 查找内部的 <component> 标签
      const componentRegex = /<component([^>]*?)>([\s\S]*?)<\/component>/gi
      let match
      let hasInnerComponents = false

      while ((match = componentRegex.exec(innerContent)) !== null) {
        hasInnerComponents = true
        const attributes = match[1]
        const componentContent = match[2].trim()

        // 解析属性
        const titleMatch = attributes.match(/title="([^"]*)"/)
        const title = titleMatch ? titleMatch[1] : undefined

        artifacts.push({
          id: `react_component_${blockIndex}_${artifacts.length}`,
          type: 'react',
          title: title || 'React 组件',
          content: componentContent,
          language: 'jsx'
        })
      }

      // 如果没有找到内部的 component 标签，将整个内容作为一个 React 组件
      if (!hasInnerComponents) {
        // 解析外层 react-components 标签的属性
        const outerTagMatch = block.match(/<react-components([^>]*?)>/i)
        const outerAttributes = outerTagMatch ? outerTagMatch[1] : ''
        
        const titleMatch = outerAttributes.match(/title="([^"]*)"/)
        const title = titleMatch ? titleMatch[1] : undefined

        artifacts.push({
          id: `react_component_${blockIndex}_0`,
          type: 'react',
          title: title || 'React 组件',
          content: innerContent.trim(),
          language: 'jsx'
        })
      }

      // 从原内容中移除这个 react-components 块
      cleanContent = cleanContent.replace(block, '')
    })
  }

  // 清理多余的空行
  cleanContent = cleanContent.replace(/\n\s*\n\s*\n/g, '\n\n').trim()

  return {
    content: cleanContent,
    artifacts
  }
}

/**
 * 解析 html-components 标签
 * 支持格式：<html-components>value</html-components>
 * 只提取标签内的 value 部分
 */
export function parseHtmlComponents(content: string): ParsedMessage {
  const artifacts: ArtifactData[] = []
  // 预处理：移除 <think> 标签
  let cleanContent = removeThinkTags(content)

  // 匹配 <html-components>...</html-components> 包装的内容
  const htmlComponentsBlockRegex = /<html-components[^>]*>([\s\S]*?)<\/html-components>/gi
  const htmlComponentsBlocks = content.match(htmlComponentsBlockRegex)

  if (htmlComponentsBlocks) {
    htmlComponentsBlocks.forEach((block, blockIndex) => {
      // 移除外层的 <html-components> 标签，只保留内部的 value
      const innerContent = block.replace(/<\/?html-components[^>]*>/gi, '')
      
      // 解析外层 html-components 标签的属性
      const outerTagMatch = block.match(/<html-components([^>]*?)>/i)
      const outerAttributes = outerTagMatch ? outerTagMatch[1] : ''
      
      const titleMatch = outerAttributes.match(/title="([^"]*)"/)
      const title = titleMatch ? titleMatch[1] : undefined

      artifacts.push({
        id: `html_component_${blockIndex}_0`,
        type: 'html',
        title: title || 'HTML 组件',
        content: innerContent.trim(), // 这里只包含 value 部分
        language: 'html'
      })

      // 从原内容中移除这个 html-components 块
      cleanContent = cleanContent.replace(block, '')
    })
  }

  // 清理多余的空行
  cleanContent = cleanContent.replace(/\n\s*\n\s*\n/g, '\n\n').trim()

  return {
    content: cleanContent,
    artifacts
  }
}

/**
 * 解析消息中的 artifacts 和 react-components
 * 支持以下格式：
 * <artifacts>
 *   <artifact type="html" title="示例页面">
 *     HTML内容
 *   </artifact>
 * </artifacts>
 * 
 * 或者简化格式：
 * <artifacts type="html" title="示例页面">
 *   HTML内容
 * </artifacts>
 */
export function parseArtifacts(content: string): ParsedMessage {
  const artifacts: ArtifactData[] = []
  
  // 预处理：移除 <think> 标签
  let cleanContent = removeThinkTags(content)

  // 首先解析 react-components
  const reactComponentsResult = parseReactComponents(cleanContent)
  artifacts.push(...reactComponentsResult.artifacts)
  cleanContent = reactComponentsResult.content

  // 然后解析 html-components
  const htmlComponentsResult = parseHtmlComponents(cleanContent)
  artifacts.push(...htmlComponentsResult.artifacts)
  cleanContent = htmlComponentsResult.content

  // 最后解析传统的 artifacts
  // 匹配 <artifacts>...</artifacts> 包装的内容
  const artifactsBlockRegex = /<artifacts[^>]*>([\s\S]*?)<\/artifacts>/gi
  const artifactsBlocks = cleanContent.match(artifactsBlockRegex)

  if (artifactsBlocks) {
    artifactsBlocks.forEach((block, blockIndex) => {
      // 移除外层的 <artifacts> 标签
      const innerContent = block.replace(/<\/?artifacts[^>]*>/gi, '')
      
      // 查找内部的 <artifact> 标签
      const artifactRegex = /<artifact([^>]*?)>([\s\S]*?)<\/artifact>/gi
      let match

      while ((match = artifactRegex.exec(innerContent)) !== null) {
        const attributes = match[1]
        const artifactContent = match[2].trim()

        // 解析属性
        const typeMatch = attributes.match(/type="([^"]*)"/)
        const titleMatch = attributes.match(/title="([^"]*)"/)
        const languageMatch = attributes.match(/language="([^"]*)"/)

        const type = typeMatch ? typeMatch[1] as ArtifactData['type'] : 'text'
        const title = titleMatch ? titleMatch[1] : undefined
        const language = languageMatch ? languageMatch[1] : undefined

        artifacts.push({
          id: `artifact_${blockIndex}_${artifacts.length}`,
          type,
          title,
          content: artifactContent,
          language
        })
      }

      // 如果没有找到内部的 artifact 标签，将整个内容作为一个 artifact
      if (!/\<artifact/.test(innerContent)) {
        // 解析外层 artifacts 标签的属性
        const outerTagMatch = block.match(/<artifacts([^>]*?)>/i)
        const outerAttributes = outerTagMatch ? outerTagMatch[1] : ''
        
        const typeMatch = outerAttributes.match(/type="([^"]*)"/)
        const titleMatch = outerAttributes.match(/title="([^"]*)"/)
        const languageMatch = outerAttributes.match(/language="([^"]*)"/)

        const type = typeMatch ? typeMatch[1] as ArtifactData['type'] : 'text'
        const title = titleMatch ? titleMatch[1] : undefined
        const language = languageMatch ? languageMatch[1] : undefined

        artifacts.push({
          id: `artifact_${blockIndex}_0`,
          type,
          title,
          content: innerContent.trim(),
          language
        })
      }

      // 从原内容中移除这个 artifacts 块
      cleanContent = cleanContent.replace(block, '')
    })
  }

  // 清理多余的空行
  cleanContent = cleanContent.replace(/\n\s*\n\s*\n/g, '\n\n').trim()

  return {
    content: cleanContent,
    artifacts
  }
}

/**
 * 检查消息是否包含 artifacts 或 react-components 或 html-components
 */
export function hasArtifacts(content: string): boolean {
  // 预处理：移除 <think> 标签
  content = removeThinkTags(content)
  return /<artifacts[^>]*>[\s\S]*?<\/artifacts>/i.test(content) || hasReactComponents(content) || hasHtmlComponents(content)
}

/**
 * 检查消息是否包含 react-components
 */
export function hasReactComponents(content: string): boolean {
  // 预处理：移除 <think> 标签
  content = removeThinkTags(content)
  return /<react-components[^>]*>[\s\S]*?<\/react-components>/i.test(content)
}

/**
 * 检查消息是否包含 html-components
 */
export function hasHtmlComponents(content: string): boolean {
  // 预处理：移除 <think> 标签
  content = removeThinkTags(content)
  return /<html-components[^>]*>[\s\S]*?<\/html-components>/i.test(content)
}

/**
 * 检查消息是否开始了 react-components 标签（用于流式检测）
 */
export function hasStartedReactComponents(content: string): boolean {
  return /<react-components[^>]*>/i.test(content)
}

/**
 * 检查消息是否开始了 html-components 标签（用于流式检测）
 */
export function hasStartedHtmlComponents(content: string): boolean {
  return /<html-components[^>]*>/i.test(content)
}

/**
 * 检查消息是否开始了 artifacts 标签（用于流式检测）
 */
export function hasStartedArtifacts(content: string): boolean {
  return /<artifacts[^>]*>/i.test(content) || hasStartedReactComponents(content) || hasStartedHtmlComponents(content) || hasStartedCodeBlocks(content)
}

/**
 * 检查消息是否开始了代码块（用于流式检测）
 */
export function hasStartedCodeBlocks(content: string): boolean {
  return /```(html|svg|jsx|react|javascript|js|typescript|ts|tsx|python|css|json)/i.test(content)
}

/**
 * 提取流式输出中的 react-components 内容
 */
export function extractStreamingReactComponentsContent(content: string): string {
  // 预处理：移除 <think> 标签
  content = removeThinkTags(content)
  
  // 尝试匹配 <react-components> 开始标签到内容结束（可能还没有结束标签）
  const match = content.match(/<react-components[^>]*>([\s\S]*?)(?:<\/react-components>|$)/i)
  if (match && match[1]) {
    return match[1].trim()
  }
  
  // 如果没有匹配到完整的标签，检查是否有开始标签
  const startMatch = content.match(/<react-components[^>]*>/i)
  if (startMatch) {
    // 找到开始标签的位置，提取后面的所有内容
    const startIndex = content.indexOf(startMatch[0]) + startMatch[0].length
    const remainingContent = content.substring(startIndex)
    
    // 检查是否有结束标签
    const endMatch = remainingContent.match(/<\/react-components>/i)
    if (endMatch) {
      return remainingContent.substring(0, endMatch.index).trim()
    } else {
      // 没有结束标签，返回所有剩余内容
      return remainingContent.trim()
    }
  }
  
  return ''
}

/**
 * 提取流式输出中的 html-components 内容
 */
export function extractStreamingHtmlComponentsContent(content: string): string {
  // 预处理：移除 <think> 标签
  content = removeThinkTags(content)
  
  // 尝试匹配 <html-components> 开始标签到内容结束（可能还没有结束标签）
  const match = content.match(/<html-components[^>]*>([\s\S]*?)(?:<\/html-components>|$)/i)
  if (match && match[1]) {
    return match[1].trim()
  }
  
  // 如果没有匹配到完整的标签，检查是否有开始标签
  const startMatch = content.match(/<html-components[^>]*>/i)
  if (startMatch) {
    // 找到开始标签的位置，提取后面的所有内容
    const startIndex = content.indexOf(startMatch[0]) + startMatch[0].length
    const remainingContent = content.substring(startIndex)
    
    // 检查是否有结束标签
    const endMatch = remainingContent.match(/<\/html-components>/i)
    if (endMatch) {
      return remainingContent.substring(0, endMatch.index).trim()
    } else {
      // 没有结束标签，返回所有剩余内容
      return remainingContent.trim()
    }
  }
  
  return ''
}

/**
 * 专门用于流式处理的内容分离函数
 * 返回应该在左侧显示的内容（移除 react-components 和 html-components 部分）
 */
export function getStreamingDisplayContent(content: string): string {
  // 预处理：移除 <think> 标签
  content = removeThinkTags(content)
  
  // 查找 react-components 开始标签
  const reactStartMatch = content.match(/<react-components[^>]*>/i)
  if (reactStartMatch) {
    // 如果找到开始标签，只返回开始标签之前的内容
    const startIndex = content.indexOf(reactStartMatch[0])
    const beforeReactComponents = content.substring(0, startIndex).trim()
    
    // 如果有结束标签，还要加上结束标签之后的内容
    const endMatch = content.match(/<\/react-components>/i)
    if (endMatch) {
      const endIndex = content.indexOf(endMatch[0]) + endMatch[0].length
      const afterReactComponents = content.substring(endIndex).trim()
      const result = beforeReactComponents + (afterReactComponents ? '\n\n' + afterReactComponents : '')
      // 如果结果为空，返回一个占位文本
      return result || '我为你创建了一个 React 组件：'
    }
    
    // 如果没有结束标签，只返回前面的内容，如果为空则返回占位文本
    return beforeReactComponents || '我为你创建了一个 React 组件：'
  }
  
  // 查找 html-components 开始标签
  const htmlStartMatch = content.match(/<html-components[^>]*>/i)
  if (htmlStartMatch) {
    // 如果找到开始标签，只返回开始标签之前的内容
    const startIndex = content.indexOf(htmlStartMatch[0])
    const beforeHtmlComponents = content.substring(0, startIndex).trim()
    
    // 如果有结束标签，还要加上结束标签之后的内容
    const endMatch = content.match(/<\/html-components>/i)
    if (endMatch) {
      const endIndex = content.indexOf(endMatch[0]) + endMatch[0].length
      const afterHtmlComponents = content.substring(endIndex).trim()
      const result = beforeHtmlComponents + (afterHtmlComponents ? '\n\n' + afterHtmlComponents : '')
      // 如果结果为空，返回一个占位文本
      return result || '我为你创建了一个 HTML 组件：'
    }
    
    // 如果没有结束标签，只返回前面的内容，如果为空则返回占位文本
    return beforeHtmlComponents || '我为你创建了一个 HTML 组件：'
  }
  
  // 如果没有找到 react-components 或 html-components 标签，返回原内容
  return content
}

/**
 * 生成 artifacts 的示例用法
 */
export function generateArtifactExample(type: ArtifactData['type'], title: string, content: string): string {
  return `<artifacts type="${type}" title="${title}">
${content}
</artifacts>`
}

/**
 * 推断文件类型
 */
export function inferArtifactType(content: string): ArtifactData['type'] {
  const trimmedContent = content.trim().toLowerCase()
  
  if (trimmedContent.startsWith('<!doctype html') || trimmedContent.startsWith('<html')) {
    return 'html'
  }
  
  if (trimmedContent.startsWith('{') || trimmedContent.startsWith('[')) {
    try {
      JSON.parse(content)
      return 'json'
    } catch {
      // 继续检查其他类型
    }
  }
  
  if (trimmedContent.includes('function ') || trimmedContent.includes('const ') || trimmedContent.includes('let ')) {
    if (trimmedContent.includes('import react') || trimmedContent.includes('jsx') || trimmedContent.includes('tsx')) {
      return 'react'
    }
    return 'javascript'
  }
  
  if (trimmedContent.includes('def ') || trimmedContent.includes('import ') || trimmedContent.includes('class ')) {
    return 'python'
  }
  
  if (trimmedContent.startsWith('#') || trimmedContent.includes('## ')) {
    return 'markdown'
  }
  
  if (trimmedContent.includes('{') && (trimmedContent.includes('color:') || trimmedContent.includes('margin:'))) {
    return 'css'
  }
  
  return 'text'
}

/**
 * 实时检测流式内容中的artifacts并立即处理
 * 这是从后端移植过来的完整检测逻辑，用于实时处理
 */
export function detectAndProcessStreamingArtifacts(content: string): {
  hasArtifacts: boolean
  artifacts: ArtifactData[]
  remainingContent: string
  shouldExpand: boolean // 是否应该立即展开artifacts面板
} {
  if (!content || !content.trim()) {
    return {
      hasArtifacts: false,
      artifacts: [],
      remainingContent: content,
      shouldExpand: false
    }
  }

  // 预处理：移除 <think> 标签，避免干扰HTML检测和React渲染
  content = removeThinkTags(content)

  // 检查是否已经包含artifacts标签，如果有则不处理
  if (content.includes('<artifacts') || content.includes('<react-components') || content.includes('<html-components')) {
    console.log('🔍 发现artifacts标签，使用parseArtifacts处理')
    const parsed = parseArtifacts(content)
    return {
      hasArtifacts: parsed.artifacts.length > 0,
      artifacts: parsed.artifacts,
      remainingContent: parsed.content,
      shouldExpand: parsed.artifacts.length > 0
    }
  }

  const artifacts: ArtifactData[] = []
  let processedContent = content
  let shouldExpand = false

  // 1. 增强的HTML代码块检测（包括SVG）
  const htmlBlockPattern = /```(?:html|svg)\s*\n([\s\S]*?)\n```/gi
  
  // 重置正则表达式的lastIndex
  htmlBlockPattern.lastIndex = 0
  const htmlBlockMatch = htmlBlockPattern.exec(content)
  
  // 添加调试日志
  if (content.includes('```html') || content.includes('```svg')) {
    console.log('🔍 HTML代码块检测:', {
      hasHtmlBlock: !!htmlBlockMatch,
      pattern: htmlBlockPattern.source,
      contentSnippet: content.substring(0, 300) + '...'
    })
  }
  
  if (htmlBlockMatch) {
    const htmlContent = htmlBlockMatch[1].trim()
    
    // 根据代码块类型确定artifact类型
    const blockType = htmlBlockMatch[0].match(/```(html|svg)/i)?.[1].toLowerCase()
    const artifactType = blockType === 'svg' ? 'svg' : 'html'
    
    // 提取title
    let title = "代码内容"
    if (artifactType === 'svg') {
      title = "SVG图形"
    } else if (/<title[^>]*>([\s\S]*?)<\/title>/i.test(htmlContent)) {
      const titleMatch = /<title[^>]*>([\s\S]*?)<\/title>/i.exec(htmlContent)
      title = titleMatch ? titleMatch[1].trim() : "HTML页面"
    } else {
      title = "HTML页面"
    }
    
    const artifact: ArtifactData = {
      id: `${artifactType}-${Date.now()}`,
      type: artifactType,
      title,
      content: htmlContent
    }
    
    artifacts.push(artifact)
    
    // 替换原内容中的代码块为占位符
    processedContent = processedContent.replace(
      htmlBlockMatch[0], 
      `\n[${artifactType === 'svg' ? '🎨' : '📄'} ${title}]\n`
    )
    shouldExpand = true
  }

  // 2. 检测完整的HTML文档（如果没有代码块包裹）
  const htmlDocPattern = /<!DOCTYPE\s+html>|<html[^>]*>[\s\S]*<\/html>/i
  
  // 添加调试日志
  if (content.includes('<!DOCTYPE html') || content.includes('<html')) {
    console.log('🔍 HTML文档检测:', {
      hasHtmlBlock: !!htmlBlockMatch,
      htmlDocPattern: htmlDocPattern.test(content),
      pattern: htmlDocPattern.source,
      contentSnippet: content.substring(0, 300) + '...'
    })
  }
  
  if (!htmlBlockMatch && htmlDocPattern.test(content)) {
    // 提取title
    const titleMatch = /<title[^>]*>([\s\S]*?)<\/title>/i.exec(content)
    const title = titleMatch ? titleMatch[1].trim() : "HTML页面"
    
    const artifact: ArtifactData = {
      id: `html-doc-${Date.now()}`,
      type: 'html',
      title,
      content: content.trim()
    }
    
    artifacts.push(artifact)
    processedContent = `[📄 ${title}]`
    shouldExpand = true
  }

  // 3. 增强的React组件代码块检测
  const reactBlockPattern = /```(?:jsx|react|javascript|js|typescript|ts|tsx)\s*\n([\s\S]*?)\n```/gi
  
  // 重置正则表达式的lastIndex
  reactBlockPattern.lastIndex = 0
  const reactBlockMatch = reactBlockPattern.exec(content)
  
  if (reactBlockMatch) {
    const reactContent = reactBlockMatch[1].trim()
    
    // 使用现有的React检测逻辑
    if (isReactComponent(reactContent)) {
      const componentName = extractComponentName(reactContent)
      const title = componentName ? `${componentName}组件` : "React组件"
      
      const artifact: ArtifactData = {
        id: `react-${Date.now()}`,
        type: 'react',
        title,
        content: reactContent
      }
      
      artifacts.push(artifact)
      
      // 替换原内容中的代码块为占位符
      processedContent = processedContent.replace(
        reactBlockMatch[0], 
        `\n[⚛️ ${title}]\n`
      )
      shouldExpand = true
    } else {
      // 即使不是React组件，如果是JavaScript/TypeScript代码，也可能包含可预览的内容
      let title = "JavaScript代码"
      let artifactType: ArtifactData['type'] = 'javascript'
      
      // 检测代码块语言类型
      const languageMatch = reactBlockMatch[0].match(/```(\w+)/)
      if (languageMatch) {
        const language = languageMatch[1].toLowerCase()
        switch (language) {
          case 'tsx':
          case 'typescript':
          case 'ts':
            title = "TypeScript代码"
            artifactType = 'javascript' // 使用javascript类型进行语法高亮
            break
          case 'jsx':
            title = "JSX代码"
            artifactType = 'react'
            break
          case 'react':
            title = "React代码"
            artifactType = 'react'
            break
          default:
            title = "JavaScript代码"
            artifactType = 'javascript'
        }
      }
      
      const artifact: ArtifactData = {
        id: `js-${Date.now()}`,
        type: artifactType,
        title,
        content: reactContent
      }
      
      artifacts.push(artifact)
      
      // 替换原内容中的代码块为占位符
      processedContent = processedContent.replace(
        reactBlockMatch[0], 
        `\n[📄 ${title}]\n`
      )
      shouldExpand = true
    }
  }

  // 4. 检测React组件内容（如果没有代码块包裹）
  if (!reactBlockMatch && !htmlBlockMatch && !htmlDocPattern.test(content)) {
    const reactContentPatterns = [
      /import\s+React/i,
      /from\s+['"]react['"]/i,
      /function\s+[A-Z]\w*\s*\([^)]*\)\s*\{/,
      /const\s+[A-Z]\w*\s*=\s*\([^)]*\)\s*=>\s*\{/,
      /useState\s*\(/,
      /useEffect\s*\(/,
      /export\s+default\s+[A-Z]\w*/,
      /return\s*\(\s*</,
      /className\s*=/,
      /onClick\s*=/,
      /from\s+['"]@mui\/material/i,
      /from\s+['"]antd/i,
      /from\s+['"]next\//i,
    ]

    const reactMatchCount = reactContentPatterns.filter(pattern => pattern.test(content)).length

    if (reactMatchCount >= 3) {
      // 尝试从内容中提取组件名或使用默认标题
      const componentName = extractComponentName(content)
      const title = componentName ? `${componentName}组件` : "React组件"
      
      const artifact: ArtifactData = {
        id: `react-content-${Date.now()}`,
        type: 'react',
        title,
        content: content.trim()
      }
      
      artifacts.push(artifact)
      processedContent = `[⚛️ ${title}]`
      shouldExpand = true
    }
  }

  // 5. 检测Chart.js配置（如果是纯配置对象，包装成HTML）
  if (!artifacts.length && 
      ((content.includes('type:') && content.includes('data:') && content.includes('options:')) || 
       (content.includes('"type"') && content.includes('"data"') && content.includes('"options"')))) {
    
    const chartHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表展示</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        canvas {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="myChart"></canvas>
    </div>
    
    <script>
        const config = ` + content.trim() + `;
        
        const ctx = document.getElementById('myChart').getContext('2d');
        new Chart(ctx, config);
    </script>
</body>
</html>`
    
    const artifact: ArtifactData = {
      id: `chart-${Date.now()}`,
      type: 'html',
      title: '图表展示',
      content: chartHtml
    }
    
    artifacts.push(artifact)
    processedContent = '[📊 图表展示]'
    shouldExpand = true
  }

  const result = {
    hasArtifacts: artifacts.length > 0,
    artifacts,
    remainingContent: processedContent,
    shouldExpand
  }
  
  // 如果检测到HTML相关内容，添加调试日志
  if (content.includes('<!DOCTYPE html') || content.includes('```html') || content.includes('<html')) {
    console.log('🔍 detectAndProcessStreamingArtifacts 结果:', {
      hasArtifacts: result.hasArtifacts,
      artifactsCount: result.artifacts.length,
      shouldExpand: result.shouldExpand,
      artifactTypes: result.artifacts.map(a => a.type),
      artifactTitles: result.artifacts.map(a => a.title)
    })
  }
  
  return result
}

/**
 * 检测代码块的完整性，用于流式检测
 */
export function isCodeBlockComplete(content: string): {
  isComplete: boolean
  blockType: 'html' | 'svg' | 'react' | 'html-components' | 'react-components' | 'unknown' | null
  startMarker: string | null
} {
  // 检测 html-components 标签
  const htmlComponentsStartMatch = /<html-components[^>]*>/i.exec(content)
  if (htmlComponentsStartMatch) {
    const htmlComponentsEndPattern = /<\/html-components>/i
    const hasEnd = htmlComponentsEndPattern.test(content.slice(htmlComponentsStartMatch.index + htmlComponentsStartMatch[0].length))
    return {
      isComplete: hasEnd,
      blockType: 'html-components',
      startMarker: htmlComponentsStartMatch[0]
    }
  }

  // 检测 react-components 标签
  const reactComponentsStartMatch = /<react-components[^>]*>/i.exec(content)
  if (reactComponentsStartMatch) {
    const reactComponentsEndPattern = /<\/react-components>/i
    const hasEnd = reactComponentsEndPattern.test(content.slice(reactComponentsStartMatch.index + reactComponentsStartMatch[0].length))
    return {
      isComplete: hasEnd,
      blockType: 'react-components',
      startMarker: reactComponentsStartMatch[0]
    }
  }

  // 检测 HTML 代码块
  const htmlStartMatch = /```html\s*\n/i.exec(content)
  if (htmlStartMatch) {
    const htmlEndPattern = /\n```/
    const hasEnd = htmlEndPattern.test(content.slice(htmlStartMatch.index + htmlStartMatch[0].length))
    return {
      isComplete: hasEnd,
      blockType: 'html',
      startMarker: htmlStartMatch[0]
    }
  }

  // 检测 SVG 代码块
  const svgStartMatch = /```svg\s*\n/i.exec(content)
  if (svgStartMatch) {
    const svgEndPattern = /\n```/
    const hasEnd = svgEndPattern.test(content.slice(svgStartMatch.index + svgStartMatch[0].length))
    return {
      isComplete: hasEnd,
      blockType: 'svg',
      startMarker: svgStartMatch[0]
    }
  }

  // 检测 React 相关代码块
  const reactPatterns = [/```jsx\s*\n/i, /```react\s*\n/i, /```javascript\s*\n/i, /```js\s*\n/i, /```typescript\s*\n/i, /```ts\s*\n/i, /```tsx\s*\n/i]
  
  for (const pattern of reactPatterns) {
    const match = pattern.exec(content)
    if (match) {
      const endPattern = /\n```/
      const hasEnd = endPattern.test(content.slice(match.index + match[0].length))
      return {
        isComplete: hasEnd,
        blockType: 'react',
        startMarker: match[0]
      }
    }
  }

  return {
    isComplete: true,
    blockType: null,
    startMarker: null
  }
}

/**
 * 实时流式检测，支持不完整的代码块
 */
export function detectStreamingArtifactsRealtime(content: string): {
  hasArtifacts: boolean
  artifacts: ArtifactData[]
  remainingContent: string
  shouldExpand: boolean
  isStreaming: boolean // 是否还在流式输出中
} {
  const blockStatus = isCodeBlockComplete(content)
  
  // 如果代码块不完整，等待更多内容
  if (!blockStatus.isComplete && blockStatus.blockType) {
    return {
      hasArtifacts: false,
      artifacts: [],
      remainingContent: content,
      shouldExpand: false,
      isStreaming: true
    }
  }

  // 代码块完整或者没有代码块，进行完整检测
  const result = detectAndProcessStreamingArtifacts(content)
  return {
    ...result,
    isStreaming: false
  }
} 