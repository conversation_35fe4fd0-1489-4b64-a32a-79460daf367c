import json
import time
from typing import List
from agent.multi_mcp_manager import create_smart_multi_mcp_agent
from config.models import ExecutionPlan

class LLMChat:
    @staticmethod
    async def create_multi_mcp_agent(mcp_names: List[str], model_name: str):
        """创建支持多个MCP服务器的代理 - 使用新的智能管理器"""
        return await create_smart_multi_mcp_agent(mcp_names, model_name)

    @staticmethod
    def _build_task_with_context(message: str, chat_history: List = None) -> str:
        """构建包含聊天历史的任务提示"""
        if not chat_history or len(chat_history) == 0:
            return message

        # 构建聊天历史上下文
        context_lines = ["以下是之前的对话历史："]
        for chat_msg in chat_history[-10:]:  # 只取最近10条消息
            # 处理不同类型的聊天消息对象
            if hasattr(chat_msg, 'role'):
                # Pydantic 模型
                role = "用户" if chat_msg.role == 'user' else "助手"
                content = getattr(chat_msg, 'content', '').strip()
            elif isinstance(chat_msg, dict):
                # 字典对象
                role = "用户" if chat_msg.get('role') == 'user' else "助手"
                content = chat_msg.get('content', '').strip()
            else:
                continue

            if content:
                context_lines.append(f"{role}: {content}")

        context_lines.append("\n现在请回答用户的新问题：")
        context_lines.append(message)

        return "\n".join(context_lines)

    @staticmethod
    async def chat_stream(message: str, mcp_names: List[str], model_name: str, chat_history: List = None):
        """流式聊天生成器"""
        agent = None
        workbenches = []
        try:
            # 创建代理和工作台
            agent, workbenches = await LLMChat.create_multi_mcp_agent(mcp_names, model_name)
            
            current_tool_calls = {}  # 存储当前的工具调用信息
            streaming_content = ""  # 累积流式内容
            has_streaming_content = False  # 标记是否有流式内容
            message_id = f"msg_{id(agent)}"  # 为这次对话生成唯一ID
            
            # 在开始处理之前，先发送执行计划并等待用户确认
            if hasattr(agent.manager, 'get_server_for_task'):
                try:
                    execution_plan = await agent.manager.get_server_for_task(message)
                    if execution_plan:
                        plan_data = {
                            "type": "execution_plan",
                            "plan": execution_plan,
                            "source": "system"
                        }
                        yield f"data: {json.dumps(plan_data, ensure_ascii=False)}\n\n"

                        # 发送等待确认的消息，不继续执行
                        waiting_data = {
                            "type": "waiting_confirmation",
                            "message": "请查看上述执行计划，确认后点击'确认并执行任务'按钮开始执行。",
                            "source": "system"
                        }
                        yield f"data: {json.dumps(waiting_data, ensure_ascii=False)}\n\n"

                        # 直接结束，等待用户确认
                        end_data = {"type": "end", "content": "等待用户确认执行计划", "source": "system"}
                        yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"
                        return

                except Exception as e:
                    print(f"⚠️ 获取执行计划失败: {e}")

            # 构建包含聊天历史的任务提示
            task_with_context = LLMChat._build_task_with_context(message, chat_history)

            # 如果没有执行计划，则直接运行对话流（后备方案）
            async for chunk in agent.run_stream(task=task_with_context):
                try:
                    # 检查是否是直接的数据行（来自多步骤执行）
                    if isinstance(chunk, str) and chunk.startswith('data: '):
                        yield chunk
                        continue
                    
                    # 检查chunk的所有属性
                    chunk_dict = chunk.__dict__ if hasattr(chunk, '__dict__') else {}
                    chunk_str = str(chunk)
                    chunk_type = getattr(chunk, 'type', '')
                    
                    # 处理工具调用请求事件
                    if chunk_type == 'ToolCallRequestEvent' and hasattr(chunk, 'content'):
                        for function_call in chunk.content:
                            try:
                                tool_call_id = getattr(function_call, 'id', f"tool_{len(current_tool_calls)}")
                                tool_name = getattr(function_call, 'name', 'unknown')
                                arguments_str = getattr(function_call, 'arguments', '{}')
                                
                                # 解析参数
                                try:
                                    parameters = json.loads(arguments_str)
                                except:
                                    parameters = {"raw_arguments": arguments_str}
                                
                                # 存储工具调用信息
                                current_tool_calls[tool_call_id] = {
                                    "tool_name": tool_name,
                                    "parameters": parameters
                                }
                                
                                # 发送工具调用开始信号
                                data = {
                                    "type": "tool_call_start",
                                    "tool_call_id": tool_call_id,
                                    "tool_name": tool_name,
                                    "parameters": parameters,
                                    "source": "system",
                                    "mcp_servers": mcp_names  # 添加当前使用的MCP服务器信息
                                }
                                yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                    
                            except Exception as e:
                                pass  # 静默处理解析错误
                        continue  # 不处理其他逻辑，直接跳到下一个chunk
                    
                    # 处理工具调用执行结果事件
                    elif chunk_type == 'ToolCallExecutionEvent' and hasattr(chunk, 'content'):
                        for execution_result in chunk.content:
                            try:
                                tool_call_id = getattr(execution_result, 'call_id', 'unknown')
                                result_content = getattr(execution_result, 'content', '')
                                is_error = getattr(execution_result, 'is_error', False)
                                
                                # 解析结果内容
                                try:
                                    parsed_result = json.loads(result_content)
                                except:
                                    parsed_result = result_content
                                
                                # 发送工具调用结果
                                data = {
                                            "type": "tool_call_result",
                                            "tool_call_id": tool_call_id,
                                            "result": parsed_result,
                                            "is_error": is_error,
                                    "source": "system"
                                }
                                yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                    
                                # 清理已完成的工具调用
                                if tool_call_id in current_tool_calls:
                                    del current_tool_calls[tool_call_id]
                                    
                            except Exception as e:
                                pass  # 静默处理解析错误
                        continue  # 不处理其他逻辑，直接跳到下一个chunk
                    
                    # 处理流式文本内容 - 用于实时显示
                    elif hasattr(chunk, 'content') and chunk.content and chunk_type == 'ModelClientStreamingChunkEvent':
                        content = str(chunk.content)
                        if content:
                            streaming_content += content
                            has_streaming_content = True
                            data = {
                                "type": "message_delta",
                                "content": content,
                                "message_id": message_id,
                                "source": "assistant"
                            }
                            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                    
                    # 处理完整的文本消息 - 用于替换流式内容以确保格式正确
                    elif hasattr(chunk, 'content') and chunk.content and chunk_type == 'TextMessage':
                        content = str(chunk.content).strip()
                        source = getattr(chunk, 'source', 'unknown')
                        
                        # 只处理助手的消息，跳过用户消息回显
                        if source != 'user' and content:
                            # 如果之前有流式内容，发送替换消息以确保markdown格式正确
                            if has_streaming_content:
                                data = {
                                    "type": "message_replace",
                                    "content": content,
                                    "message_id": message_id,
                                    "source": "assistant"
                                }
                                yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                            else:
                                # 如果没有流式内容，直接发送完整消息
                                data = {
                                    "type": "message",
                                    "content": content,
                                    "message_id": message_id,
                                    "source": "assistant"
                                }
                                yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                                
                except Exception as chunk_error:
                    continue  # 静默处理chunk错误
                
        except Exception as e:
            error_data = {
                "type": "error",
                "content": f"处理请求时出错: {str(e)}",
                "source": "system"
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
        
        finally:
            # 清理智能代理
            if agent and hasattr(agent, 'cleanup'):
                try:
                    await agent.cleanup()
                except:
                    pass
            
            # 备用清理：直接清理workbenches
            for workbench in workbenches:
                try:
                    await workbench.__aexit__(None, None, None)
                except:
                    pass
            
            # 发送结束信号
            end_data = {"type": "end", "content": "对话完成", "source": "system"}
            yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"

    @staticmethod
    async def execute_plan_stream(plan: ExecutionPlan, mcp_names: List[str], model_name: str, original_message: str = None, chat_history: List = None):
        """执行用户确认的计划"""
        agent = None
        workbenches = []
        try:
            # 创建代理和工作台
            agent, workbenches = await LLMChat.create_multi_mcp_agent(mcp_names, model_name)

            # 将ExecutionPlan转换为字典格式，以便传递给manager
            plan_dict = {
                "use_mcp": plan.use_mcp,
                "reason": plan.reason,
                "plan": [
                    {
                        "step": step.step,
                        "action": step.action,
                        "server": step.server,
                        "description": step.description,
                        "status": step.status or "pending"
                    }
                    for step in plan.plan
                ]
            }

            # 使用原始用户消息，如果没有则使用计划原因
            execution_message = original_message or plan.reason

            # 构建包含聊天历史的执行消息
            execution_message_with_context = LLMChat._build_task_with_context(execution_message, chat_history)

            print(f"🚀 开始执行计划，原始消息: {execution_message}")
            print(f"📋 计划原因: {plan.reason}")
            print(f"🧠 聊天历史条数: {len(chat_history) if chat_history else 0}")

            # 设置manager的执行计划，跳过规划阶段
            if hasattr(agent.manager, 'set_execution_plan'):
                agent.manager.set_execution_plan(plan_dict)

            # 运行执行流程，使用包含上下文的消息
            async for chunk in agent.manager.route_message(execution_message_with_context):
                yield chunk

        except Exception as e:
            error_data = {
                "type": "error",
                "content": f"执行计划时出错: {str(e)}",
                "source": "system"
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

        finally:
            # 清理智能代理
            if agent and hasattr(agent, 'cleanup'):
                try:
                    await agent.cleanup()
                except:
                    pass

            # 备用清理：直接清理workbenches
            for workbench in workbenches:
                try:
                    await workbench.__aexit__(None, None, None)
                except:
                    pass

            # 发送结束信号
            end_data = {"type": "end", "content": "计划执行完成", "source": "system"}
            yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"

    @staticmethod
    async def execute_plan_step_by_step(plan: ExecutionPlan, mcp_names: List[str], model_name: str, original_message: str = None, chat_history: List = None):
        """分步执行用户确认的计划 - 每一步单独调用Agent"""
        agent = None
        workbenches = []
        step_results = []  # 存储每个步骤的结果
        
        try:
            # 创建代理和工作台
            agent, workbenches = await LLMChat.create_multi_mcp_agent(mcp_names, model_name)
            
            # 使用原始用户消息，如果没有则使用计划原因
            execution_message = original_message or plan.reason
            
            print(f"🚀 开始分步执行计划，原始消息: {execution_message}")
            print(f"📋 计划原因: {plan.reason}")
            print(f"📝 总步骤数: {len(plan.plan)}")
            
            # 逐步执行计划中的每个步骤
            for step_index, step in enumerate(plan.plan):
                step_number = step.step
                action = step.action
                server = step.server
                description = step.description
                
                # 为每个步骤创建新的消息气泡
                step_message_id = f"step_{step_number}_{int(time.time() * 1000)}"
                
                # 发送新消息开始信号
                new_message_data = {
                    "type": "new_message",
                    "message_id": step_message_id,
                    "step": step_number,
                    "description": description,
                    "source": "assistant"
                }
                yield f"data: {json.dumps(new_message_data, ensure_ascii=False)}\n\n"
                
                # 发送步骤开始信号
                step_start_data = {
                    "type": "step_start",
                    "step": step_number,
                    "action": action,
                    "description": description,
                    "message_id": step_message_id,
                    "source": "system"
                }
                yield f"data: {json.dumps(step_start_data, ensure_ascii=False)}\n\n"
                
                # 构建当前步骤的任务提示词
                task_prompt = await LLMChat._build_step_prompt(
                    step=step,
                    original_message=execution_message,
                    previous_results=step_results,
                    chat_history=chat_history
                )
                
                print(f"🔄 执行步骤 {step_number}: {description}")
                print(f"📝 任务提示词: {task_prompt[:200]}...")
                
                # 添加步骤标题
                step_title = f"**步骤 {step_number}: {description}**\n\n"
                title_data = {
                    "type": "message_delta",
                    "content": step_title,
                    "message_id": step_message_id,
                    "source": "assistant"
                }
                yield f"data: {json.dumps(title_data, ensure_ascii=False)}\n\n"
                
                # 执行当前步骤
                step_result = ""
                try:
                    if action == "call_mcp" and server and server in agent.manager.agents:
                        # 使用指定的MCP服务器
                        mcp_agent = agent.manager.agents[server]
                        async for chunk in LLMChat._execute_single_step(mcp_agent, task_prompt, step_number, step_message_id):
                            if isinstance(chunk, str) and chunk.startswith('step_result:'):
                                step_result = chunk.replace('step_result:', '')
                            else:
                                yield chunk
                    
                    elif action == "direct_response":
                        # 使用后备代理或第一个可用代理
                        response_agent = agent.manager.fallback_agent or list(agent.manager.agents.values())[0]
                        async for chunk in LLMChat._execute_single_step(response_agent, task_prompt, step_number, step_message_id):
                            if isinstance(chunk, str) and chunk.startswith('step_result:'):
                                step_result = chunk.replace('step_result:', '')
                            else:
                                yield chunk
                    
                    elif action == "summary":
                        # 执行总结步骤
                        summary_agent = agent.manager.fallback_agent or list(agent.manager.agents.values())[0]
                        async for chunk in LLMChat._execute_single_step(summary_agent, task_prompt, step_number, step_message_id):
                            if isinstance(chunk, str) and chunk.startswith('step_result:'):
                                step_result = chunk.replace('step_result:', '')
                            else:
                                yield chunk
                    
                    # 存储步骤结果
                    if step_result.strip():
                        step_results.append({
                            "step": step_number,
                            "action": action,
                            "description": description,
                            "result": step_result.strip()
                        })
                    
                    print(f"✅ 步骤 {step_number} 执行完成")
                    
                except Exception as step_error:
                    error_message = f"步骤 {step_number} 执行失败: {str(step_error)}"
                    print(f"❌ {error_message}")
                    
                    error_data = {
                        "type": "message_delta",
                        "content": f"\n⚠️ {error_message}\n\n",
                        "message_id": step_message_id,
                        "source": "assistant"
                    }
                    yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                    
                    # 记录错误步骤
                    step_results.append({
                        "step": step_number,
                        "action": action,
                        "description": description,
                        "result": f"执行失败: {error_message}",
                        "error": True
                    })
                
                # 发送步骤完成信号
                step_complete_data = {
                    "type": "step_complete",
                    "step": step_number,
                    "action": action,
                    "source": "system"
                }
                yield f"data: {json.dumps(step_complete_data, ensure_ascii=False)}\n\n"
                
        except Exception as e:
            error_data = {
                "type": "error",
                "content": f"分步执行计划时出错: {str(e)}",
                "source": "system"
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"

        finally:
            # 清理智能代理
            if agent and hasattr(agent, 'cleanup'):
                try:
                    await agent.cleanup()
                except:
                    pass

            # 备用清理：直接清理workbenches
            for workbench in workbenches:
                try:
                    await workbench.__aexit__(None, None, None)
                except:
                    pass

            # 发送结束信号
            end_data = {"type": "end", "content": "分步执行计划完成", "source": "system"}
            yield f"data: {json.dumps(end_data, ensure_ascii=False)}\n\n"

    @staticmethod
    async def _build_step_prompt(step, original_message: str, previous_results: List, chat_history: List = None) -> str:
        """构建步骤的任务提示词，包含上下文信息"""
        
        # 基础任务描述
        base_prompt = f"原始任务: {original_message}\n\n"
        base_prompt += f"当前步骤: {step.description}\n\n"
        
        # 添加聊天历史上下文（简化版）
        if chat_history and len(chat_history) > 0:
            base_prompt += "相关对话历史:\n"
            # 只取最近几条对话作为上下文
            recent_history = chat_history[-3:] if len(chat_history) > 3 else chat_history
            for msg in recent_history:
                # 兼容字典和对象两种格式
                if hasattr(msg, 'get'):
                    # 字典格式
                    role = "用户" if msg.get("role") == "user" else "助手"
                    content = msg.get("content", "")[:200]  # 截断过长的内容
                elif hasattr(msg, 'role') and hasattr(msg, 'content'):
                    # 对象格式
                    role = "用户" if msg.role == "user" else "助手"
                    content = msg.content[:200] if msg.content else ""  # 截断过长的内容
                else:
                    # 其他格式，跳过
                    continue
                base_prompt += f"- {role}: {content}\n"
            base_prompt += "\n"
        
        # 添加前面步骤的结果作为上下文
        if previous_results:
            base_prompt += "前面步骤的执行结果:\n"
            for result in previous_results:
                step_num = result.get("step")
                action = result.get("action")
                desc = result.get("description")
                res = result.get("result", "")[:300]  # 截断过长的结果
                if result.get("error"):
                    base_prompt += f"- 步骤{step_num}({action}): {desc} -> 执行失败: {res}\n"
                else:
                    base_prompt += f"- 步骤{step_num}({action}): {desc} -> {res}\n"
            base_prompt += "\n"
        
        # 根据步骤类型添加具体指导
        if step.action == "call_mcp":
            base_prompt += f"请使用{step.server}相关工具来完成这个步骤。"
        elif step.action == "summary":
            base_prompt += "请基于前面步骤的结果进行总结。"
        elif step.action == "direct_response":
            base_prompt += "请直接回答或处理这个步骤。"
        
        return base_prompt

    @staticmethod
    async def _execute_single_step(agent, task_prompt: str, step_number: int, message_id: str = None):
        """执行单个步骤"""
        step_result = ""
        
        try:
            async for chunk in agent.run_stream(task=task_prompt):
                chunk_type = getattr(chunk, 'type', '')
                
                # 处理工具调用请求事件
                if chunk_type == 'ToolCallRequestEvent' and hasattr(chunk, 'content'):    
                    for function_call in chunk.content:
                        try:
                            tool_call_id = getattr(function_call, 'id', f"tool_{step_number}")
                            tool_name = getattr(function_call, 'name', 'unknown')
                            arguments_str = getattr(function_call, 'arguments', '{}')
                            
                            try:
                                parameters = json.loads(arguments_str)
                            except:
                                parameters = {"raw_arguments": arguments_str}
                            
                            data = {
                                "type": "tool_call_start",
                                "tool_call_id": tool_call_id,
                                "tool_name": tool_name,
                                "parameters": parameters,
                                "source": "system",
                                "step": step_number,
                                "message_id": message_id
                            }
                            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                        except Exception as e:
                            print(f"⚠️ [DEBUG] 解析工具调用请求失败: {e}")

                # 处理工具调用执行结果事件
                elif chunk_type == 'ToolCallExecutionEvent' and hasattr(chunk, 'content'):
                    for execution_result in chunk.content:
                        try:
                            tool_call_id = getattr(execution_result, 'call_id', 'unknown')
                            result_content = getattr(execution_result, 'content', '')
                            is_error = getattr(execution_result, 'is_error', False)
                            
                            try:
                                parsed_result = json.loads(result_content)
                            except:
                                parsed_result = result_content
                            
                            data = {
                                "type": "tool_call_result",
                                "tool_call_id": tool_call_id,
                                "result": parsed_result,
                                "is_error": is_error,
                                "source": "system",
                                "step": step_number,
                                "message_id": message_id
                            }
                            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                        except Exception as e:
                            print(f"⚠️ [DEBUG] 解析工具调用结果失败: {e}")
                
                elif chunk_type == 'ModelClientStreamingChunkEvent' and hasattr(chunk, 'content'):
                    # 流式增量内容
                    content = str(chunk.content)
                    step_result += content
                    
                    data = {
                        "type": "message_delta",
                        "content": content,
                        "source": "assistant",
                        "message_id": message_id
                    }
                    yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                    
                elif chunk_type == 'TextMessage' and hasattr(chunk, 'content'):
                    # 完整消息内容
                    content = str(chunk.content).strip()
                    if content:
                        step_result = content
        
        except Exception as e:
            # 处理Agent执行错误
            error_message = f"步骤执行出现错误: {str(e)}"
            
            error_data = {
                "type": "message_delta",
                "content": f"\n⚠️ {error_message}\n\n",
                "message_id": message_id,
                "source": "assistant"
            }
            yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
            
            step_result = f"执行出错: {error_message}"
        
        # 返回步骤结果供上层使用
        yield f"step_result:{step_result}"
