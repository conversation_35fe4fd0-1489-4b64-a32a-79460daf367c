import json
from typing import List, Dict, Union
import asyncio
from agent.llm_service import Model<PERSON>anager
from autogen_core.models import UserMessage
from config.prompt import create_planner_prompt


class Planner:
    def __init__(self, model_name):
        # 创建模型管理器实例
        model_manager = ModelManager()
        self.model_client = model_manager.create_client(model_name)        
        self.model_config = model_manager.get_model_config(model_name)
        self.mcp_descriptions = self._load_mcp_descriptions()
    
    def _load_mcp_descriptions(self) -> Dict[str, str]:
        """加载MCP服务器描述"""
        try:
            with open("config/mcp_description.json", 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载MCP描述文件失败: {e}")
            return {}
    
    async def intent_recognition(self, user_message: str, available_servers: List[str]) -> Dict[str, Union[str, List[Dict]]]:
        """智能意图识别和任务规划"""
        if not available_servers:
            return {
                "use_mcp": "no",
                "reason": "没有可用的MCP服务器",
                "plan": [
                    {
                        "step": 1,
                        "action": "direct_response",
                        "description": "直接回答用户问题",
                        "task_prompt": f"请回答以下问题：{user_message}"
                    }
                ]
            }

        # 构建可用服务器的描述
        server_descriptions = []
        for server in available_servers:
            description = self.mcp_descriptions.get(server, f"{server} MCP服务器")
            server_descriptions.append(f"- {server}: {description}")
        
        # 构建AI提示词
        prompt = create_planner_prompt(
            user_message=user_message,
            server_descriptions=chr(10).join(server_descriptions)
        )
        
        try:
            result_response = await self.model_client.create([UserMessage(content=prompt, source="user")])
            result_text = result_response.content
            
            # 尝试解析JSON
            try:
                # 清理可能的代码块标记
                cleaned_text = result_text.strip()
                if cleaned_text.startswith('```json'):
                    cleaned_text = cleaned_text[7:]  # 移除 ```json
                if cleaned_text.endswith('```'):
                    cleaned_text = cleaned_text[:-3]  # 移除 ```
                cleaned_text = cleaned_text.strip()
                
                result = json.loads(cleaned_text)
                
                # 验证结果格式
                if "use_mcp" in result and "plan" in result:
                    # 验证服务器名称是否有效
                    for step in result.get("plan", []):
                        if step.get("action") == "call_mcp" and step.get("server"):
                            if step["server"] not in available_servers:
                                # 尝试模糊匹配
                                matched = False
                                for server in available_servers:
                                    if server.lower() in step["server"].lower() or step["server"].lower() in server.lower():
                                        step["server"] = server
                                        matched = True
                                        break
                                if not matched:
                                    step["server"] = available_servers[0]
                        
                        # 确保每个步骤都有task_prompt
                        if "task_prompt" not in step:
                            # 根据action类型设置合适的task_prompt，避免直接使用用户原始问题
                            if step.get("action") == "call_mcp":
                                step["task_prompt"] = f"使用相关工具来处理：{step.get('description', user_message)}"
                            elif step.get("action") == "summary":
                                step["task_prompt"] = f"总结信息：{step.get('description', '总结相关内容')}"
                            else:
                                step["task_prompt"] = f"处理任务：{step.get('description', user_message)}"
                    
                    return result
                    
            except json.JSONDecodeError:
                print(f"⚠️ AI返回的JSON格式无效: {result_text}")
            
            # 如果AI无法给出有效规划，返回默认计划
            print(f"⚠️ AI意图识别失败，使用默认规划")
            return {
                "use_mcp": "yes" if available_servers else "no",
                "reason": "使用默认规划",
                "plan": [
                    {
                        "step": 1,
                        "action": "call_mcp" if available_servers else "direct_response",
                        "server": available_servers[0] if available_servers else None,
                        "description": f"使用{available_servers[0]}处理请求" if available_servers else "直接回答问题",
                        "task_prompt": f"使用相关工具来处理：{user_message}" if available_servers else f"请回答以下问题：{user_message}"
                    }
                ]
            }
            
        except Exception as e:
            print(f"❌ AI意图识别异常: {e}")
            # 异常情况下返回默认规划
            return {
                "use_mcp": "yes" if available_servers else "no",
                "reason": "异常情况下的默认规划",
                "plan": [
                    {
                        "step": 1,
                        "action": "call_mcp" if available_servers else "direct_response",
                        "server": available_servers[0] if available_servers else None,
                        "description": f"使用{available_servers[0]}处理请求" if available_servers else "直接回答问题",
                        "task_prompt": f"使用相关工具来处理：{user_message}" if available_servers else f"请回答以下问题：{user_message}"
                    }
                ]
            }


if __name__ == "__main__":
    planner = Planner()
    result = asyncio.run(planner.intent_recognition("我想要画一个折线图", ["amap-maps", "quickchart-server"]))
    print(json.dumps(result, ensure_ascii=False, indent=2))


    