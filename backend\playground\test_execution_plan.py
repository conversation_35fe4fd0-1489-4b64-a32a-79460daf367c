#!/usr/bin/env python3
"""
测试执行计划功能
"""

import asyncio
import json
from config.models import ExecutionPlan, PlanStep, ExecutePlanRequest
from agent.llm_chat import LLMChat

async def test_execution_plan():
    """测试执行计划功能"""
    
    # 创建一个测试执行计划
    test_plan = ExecutionPlan(
        use_mcp="yes",
        reason="用户确认的测试计划",
        plan=[
            PlanStep(
                step=1,
                action="direct_response",
                description="介绍任务开始",
                status="pending"
            ),
            PlanStep(
                step=2,
                action="call_mcp",
                server="amap-maps",
                description="查询北京天气信息",
                status="pending"
            ),
            PlanStep(
                step=3,
                action="summary",
                description="总结天气信息",
                status="pending"
            )
        ]
    )
    
    print("🧪 开始测试执行计划功能")
    print(f"📋 测试计划: {test_plan.reason}")
    print(f"📝 步骤数量: {len(test_plan.plan)}")
    
    # 测试执行计划
    try:
        print("\n🚀 开始执行计划...")
        async for chunk in LLMChat.execute_plan_stream(
            plan=test_plan,
            mcp_names=["amap-maps"],
            model_name="deepseek-r1-0528"
        ):
            # 解析并显示流式数据
            if chunk.startswith('data: '):
                try:
                    data = json.loads(chunk[6:])
                    chunk_type = data.get('type', 'unknown')
                    
                    if chunk_type == 'step_start':
                        print(f"🔄 开始步骤 {data.get('step')}: {data.get('action')}")
                    elif chunk_type == 'step_complete':
                        print(f"✅ 完成步骤 {data.get('step')}")
                    elif chunk_type == 'message_delta':
                        print(data.get('content', ''), end='')
                    elif chunk_type == 'end':
                        print(f"\n🎉 {data.get('content', '执行完成')}")
                        break
                    elif chunk_type == 'error':
                        print(f"\n❌ 错误: {data.get('content')}")
                        break
                except json.JSONDecodeError:
                    pass
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

async def test_plan_modification():
    """测试计划修改功能"""
    
    print("\n🔧 测试计划修改功能")
    
    # 原始计划
    original_plan = ExecutionPlan(
        use_mcp="yes",
        reason="原始计划",
        plan=[
            PlanStep(step=1, action="direct_response", description="步骤1"),
            PlanStep(step=2, action="call_mcp", description="步骤2")
        ]
    )
    
    print(f"📋 原始计划步骤数: {len(original_plan.plan)}")
    
    # 修改计划 - 添加步骤
    modified_plan = ExecutionPlan(
        use_mcp="yes",
        reason="用户修改后的计划",
        plan=[
            PlanStep(step=1, action="direct_response", description="修改后的步骤1"),
            PlanStep(step=2, action="call_mcp", description="修改后的步骤2"),
            PlanStep(step=3, action="summary", description="新增的总结步骤")
        ]
    )
    
    print(f"📋 修改后计划步骤数: {len(modified_plan.plan)}")
    print("✅ 计划修改测试通过")

if __name__ == "__main__":
    print("🧪 执行计划功能测试")
    print("=" * 50)
    
    # 运行测试
    asyncio.run(test_plan_modification())
    
    # 如果需要测试实际执行，取消下面的注释
    # asyncio.run(test_execution_plan())
    
    print("\n✅ 所有测试完成")
