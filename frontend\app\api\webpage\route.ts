import { NextRequest, NextResponse } from 'next/server'
import Sandbox from "@e2b/code-interpreter"

export const runtime = 'nodejs'       // ⬅ 允许长时间运行
export const maxDuration = 300        // ⬅ 5 分钟

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      mode = "detailed",
      page_title = "AI生成的React页面", 
      description = "一个由AI自动生成的React组件页面",
      requirements = "创建一个现代化的网页",
      style = "现代简洁",
      features = ["响应式设计", "交互式组件"],
      query = "",
      deploy_type = "none", // "none", "server", "vercel"
      deploy_server = "",
      deploy_user = "",
      deploy_password = "",
      deploy_path = "",
      deploy_port = "3000",
      vercel_token = "",
      vercel_org_id = ""
    } = body

    // 检查API Keys
    const e2bApiKey = process.env.E2B_API_KEY
    const openaiApiKey = process.env.OPENAI_API_KEY
    
    if (!e2bApiKey) {
      return NextResponse.json(
        { 
          success: false, 
          error: "E2B API Key not configured",
          logs: ["❌ E2B API Key未配置"]
        },
        { status: 500 }
      )
    }

    if (!openaiApiKey) {
      return NextResponse.json(
        { 
          success: false, 
          error: "OpenAI API Key not configured",
          logs: ["❌ OpenAI API Key未配置"]
        },
        { status: 500 }
      )
    }

    const encoder = new TextEncoder()
    /** 流关闭标记，任何 enqueue 前务必检查 */
    let closed = false

    // 建立可写流
    const stream = new ReadableStream({
      start(controller) {
        /** 写入统一走这里 */
        const write = (obj: unknown) => {
          if (closed) return
          try {
            controller.enqueue(encoder.encode(JSON.stringify(obj) + '\n'))
          } catch {
            closed = true          // 避免后续再写
          }
        }

        /** 结束输出，只允许调用一次 */
        const finish = () => {
          if (closed) return
          closed = true
          try {
            controller.close()
          } catch { /* 已关闭亦可忽略 */ }
        }

        // ------- 核心任务 ----------
        ;(async () => {
          try {
            // AI代码生成函数
            async function generateReactCode() {
              write({ type: 'log', message: `🤖 正在生成您的强大应用（${mode === 'simple' ? '一句话生成应用' : '一表单生成应用'}模式）...` })
              
              let prompt
              
              if (mode === 'simple') {
                // 简单模式：让AI自动解析需求
                prompt = `你是一个专业的React开发专家。请根据用户的描述自动分析需求并生成一个完整的Next.js React组件代码。

用户需求描述：
${query}

请你自动分析上述需求，然后生成相应的React组件。

技术要求：
1. 使用 Next.js 15 App Router
2. 必须包含 'use client' 指令
3. 使用 @/components/ui/{组件名小写} 引用组件库，可用组件：Button, Card, CardContent, CardHeader, CardTitle, CardDescription, Badge, Input等等的shadcn ui
4. 使用 Tailwind CSS 进行样式设计
5. 包含 React Hooks (useState, useEffect 等)
6. 响应式设计，支持移动端
7. 包含交互式元素和动画效果
8. 现代化的UI设计
9. 根据用户描述自动设计合适的功能和界面

请将完整的React组件代码放在\`\`\`react 代码块中，不需要其他解释。代码应该是可以直接使用的，包含所有必要的import语句。`
              } else {
                // 详细模式：使用具体配置
                prompt = `你是一个专业的React开发专家。请根据以下需求生成一个完整的Next.js React组件代码：

项目需求：
- 页面标题：${page_title}
- 页面描述：${description}
- 具体要求：${requirements}
- 设计风格：${style}
- 需要的功能：${Array.isArray(features) ? features.join(', ') : features}

技术要求：
1. 使用 Next.js 15 App Router
2. 必须包含 'use client' 指令
3. 使用 @/components/ui/{组件名小写} 引用组件库，可用组件：Button, Card, CardContent, CardHeader, CardTitle, CardDescription, Badge, Input等等的shadcn ui
4. 使用 Tailwind CSS 进行样式设计
5. 包含 React Hooks (useState, useEffect 等)
6. 响应式设计，支持移动端
7. 包含交互式元素和动画效果
8. 现代化的UI设计

请将完整的React组件代码放在\`\`\`react 代码块中，不需要其他解释。代码应该是可以直接使用的，包含所有必要的import语句。`
              }

              try {
                const response = await fetch('https://api.tu-zi.com/v1/chat/completions', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${openaiApiKey}`,
                  },
                  body: JSON.stringify({
                    model: 'claude-sonnet-4-20250514',
                    max_tokens: 4000,
                    temperature: 0.7,
                    messages: [
                      {
                        role: 'system',
                        content: '你是一个专业的React开发专家，擅长创建现代化、响应式的React组件。'
                      },
                      {
                        role: 'user',
                        content: prompt
                      }
                    ],
                  }),
                })

                if (!response.ok) {
                  throw new Error(`OpenAI API error: ${response.status}`)
                }

                const data = await response.json()
                const generatedContent = data.choices[0]?.message?.content

                if (!generatedContent) {
                  throw new Error('No content generated from OpenAI')
                }

                // 提取 ```react ``` 代码块中的内容
                const reactCode = extractReactCode(generatedContent)
                
                if (!reactCode) {
                  throw new Error('No React code block found in generated content')
                }

                write({ type: 'log', message: "✅ AI代码生成成功" })
                return reactCode

              } catch (error) {
                write({ type: 'log', message: `❌ AI代码生成失败: ${error}` })
                // 返回备用模板
                return generateFallbackCode()
              }
            }

            // 提取React代码块的函数
            function extractReactCode(content: string): string | null {
              // 匹配 ```react 或 ```jsx 或 ```tsx 代码块
              const codeBlockRegex = /```(?:react|jsx|tsx)\n([\s\S]*?)\n```/i
              const match = content.match(codeBlockRegex)
              
              if (match && match[1]) {
                return match[1].trim()
              }
              
              // 如果没找到特定的代码块，尝试匹配普通的代码块
              const genericCodeBlockRegex = /```\n([\s\S]*?)\n```/
              const genericMatch = content.match(genericCodeBlockRegex)
              
              if (genericMatch && genericMatch[1]) {
                const code = genericMatch[1].trim()
                // 检查是否包含React相关内容
                if (code.includes('export default') && (code.includes('React') || code.includes('useState') || code.includes('jsx'))) {
                  return code
                }
              }
              
              return null
            }

            // 备用代码模板
            function generateFallbackCode() {
              const title = mode === 'simple' ? 'AI 智能生成页面' : page_title
              const desc = mode === 'simple' ? '基于您的描述自动生成的 React 组件' : description
              const reqs = mode === 'simple' ? query.slice(0, 100) : requirements
              
              return `'use client'

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function Home() {
  const [count, setCount] = useState(0)

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <Badge variant="secondary" className="mb-4">
            ✨ AI Generated
          </Badge>
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            ${title}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
            ${desc}
          </p>
        </div>
        
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>交互演示</CardTitle>
            <CardDescription>${reqs}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-4">
              <div className="text-4xl font-bold text-gray-700">
                {count}
              </div>
              <Button 
                onClick={() => setCount(count + 1)}
                className="w-full"
              >
                点击计数
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}`
            }

            // Vercel部署函数
            async function deployToVercel(sandbox: Sandbox) {
              write({ type: 'log', message: "🚀 开始部署到Vercel..." })
              
              // 首先检查目录和准备部署
              const prepareResult = await sandbox.runCode(`
                # 检查当前目录和项目结构
                echo "📋 当前工作目录：$(pwd)"
                echo "📂 目录内容："
                ls -la
                
                # 智能检查项目目录
                if [ -f "package.json" ]; then
                  echo "✅ 当前目录就是项目根目录"
                  echo "📋 package.json 检查："
                  cat package.json | head -20
                  DEPLOY_DIR="."
                elif [ -d "webpage-base-template" ]; then
                  echo "✅ 找到 webpage-base-template 子目录"
                  cd webpage-base-template
                  echo "📋 项目目录内容："
                  ls -la
                  echo "📋 package.json 检查："
                  if [ -f package.json ]; then
                    echo "✅ package.json 存在"
                    cat package.json | head -20
                  else
                    echo "❌ package.json 不存在"
                    exit 1
                  fi
                  DEPLOY_DIR="webpage-base-template"
                else
                  echo "❌ 未找到项目目录或 package.json"
                  exit 1
                fi
                
                echo "🎯 部署目录: $DEPLOY_DIR"
              `, { language: "bash", timeoutMs: 60000 })
              
              if (prepareResult.error) {
                write({ type: 'log', message: `❌ Vercel部署准备失败: ${prepareResult.error}` })
                return null
              }
              
              if (prepareResult.logs?.stdout) {
                write({ type: 'log', message: `📄 Vercel部署准备: ${prepareResult.logs.stdout.join('')}` })
              }

              // 执行Vercel部署
              const deploy = await sandbox.runCode(`
                # 智能确定工作目录
                if [ -f "package.json" ]; then
                  echo "✅ 在项目根目录执行部署"
                  WORK_DIR="."
                elif [ -d "webpage-base-template" ]; then
                  echo "✅ 切换到 webpage-base-template 目录"
                  cd webpage-base-template
                  WORK_DIR="webpage-base-template"
                else
                  echo "❌ 无法找到项目目录"
                  exit 1
                fi
                
                echo "📍 当前工作目录: $(pwd)"
                
                # 安装Vercel CLI
                echo "📦 安装Vercel CLI..."
                npm install -g vercel@latest || {
                  echo "❌ Vercel CLI安装失败"
                  exit 1
                }
                
                # 验证Vercel token
                echo "🔑 验证Vercel token..."
                export VERCEL_TOKEN="${vercel_token}"
                vercel whoami || {
                  echo "❌ Vercel token验证失败，请检查token是否正确"
                  exit 1
                }
                
                echo "✅ Vercel token验证成功"
                
                # 清理旧的.vercel目录
                rm -rf .vercel
                
                # 配置Vercel项目
                echo "🔧 配置Vercel项目..."
                
                # 创建.env.production文件（Vercel专用）
                cat > .env.production << 'ENV_PROD'
NEXT_TELEMETRY_DISABLED=1
ENV_PROD
                
                # 创建vercel.json配置文件
                cat > vercel.json << 'VERCEL_CONFIG'
{
  "version": 2,
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "outputDirectory": ".next",
  "env": {
    "NEXT_TELEMETRY_DISABLED": "1"
  },
  "functions": {
    "app/**/*.{js,ts,tsx}": {
      "maxDuration": 30
    }
  }
}
VERCEL_CONFIG
                
                echo "✅ Vercel配置文件创建完成"
                
                # 部署到Vercel
                echo "🚀 部署到Vercel..."
                if [ -n "${vercel_org_id}" ]; then
                  # 使用指定的组织
                  vercel --prod --yes --token="${vercel_token}" --scope="${vercel_org_id}" --force || {
                    echo "❌ Vercel部署失败"
                    exit 1
                  }
                else
                  # 使用个人账户
                  vercel --prod --yes --token="${vercel_token}" --force || {
                    echo "❌ Vercel部署失败"
                    exit 1
                  }
                fi
                
                echo "🎉 Vercel部署成功完成！"
                
              `, { language: "bash", timeoutMs: 5 * 60 * 1000 })

              if (deploy.logs?.stdout) {
                write({ type: 'log', message: `📄 Vercel部署日志: ${deploy.logs.stdout.join('')}` })
              }
              if (deploy.logs?.stderr) {
                write({ type: 'log', message: `❌ Vercel部署错误: ${deploy.logs.stderr.join('')}` })
              }
              
              // 检查是否是超时或终止错误
              if (deploy.error) {
                write({ type: 'log', message: `⚠️ Vercel部署遇到问题: ${deploy.error}` })
                return null
              }

              // 提取部署 URL
              const allLogs = (deploy.logs?.stdout || []).concat(deploy.logs?.stderr || []).join('')
              
              // 尝试多种URL匹配模式
              const urlPatterns = [
                /https:\/\/[^\s]+\.vercel\.app/g,
                /Preview: (https:\/\/[^\s]+\.vercel\.app)/g,
                /Production: (https:\/\/[^\s]+\.vercel\.app)/g,
                /✅ Production: (https:\/\/[^\s]+\.vercel\.app)/g
              ]
              
              let deployUrl = null
              for (const pattern of urlPatterns) {
                const matches = allLogs.match(pattern)
                if (matches && matches.length > 0) {
                  // 获取最后一个匹配（通常是生产环境URL）
                  const lastMatch = matches[matches.length - 1]
                  deployUrl = lastMatch.includes('(') ? lastMatch.match(/\((https:\/\/[^)]+)\)/)?.[1] : lastMatch
                  if (deployUrl) break
                }
              }
              
              if (deployUrl) {
                write({ type: 'log', message: `🎉 Vercel部署成功！访问地址：${deployUrl}` })
                return deployUrl
              } else {
                write({ type: 'log', message: "⚠️ Vercel部署完成，但未能提取访问 URL" })
                write({ type: 'log', message: `📋 完整日志用于调试：${allLogs.slice(-500)}` }) // 显示最后500字符用于调试
                return null
              }
            }

            // 自定义服务器部署函数
            async function deployToServer(sandbox: Sandbox) {
                write({ type: 'log', message: "🚀 开始部署到自定义服务器..." })
                
                // 首先检查目录和准备部署
                const prepareResult = await sandbox.runCode(`
                  # 检查当前目录和项目结构
                  echo "📋 当前工作目录：$(pwd)"
                  echo "📂 目录内容："
                  ls -la
                  
                  # 智能检查项目目录
                  if [ -f "package.json" ]; then
                    echo "✅ 当前目录就是项目根目录"
                    echo "📋 package.json 检查："
                    cat package.json | head -20
                    DEPLOY_DIR="."
                  elif [ -d "webpage-base-template" ]; then
                    echo "✅ 找到 webpage-base-template 子目录"
                    cd webpage-base-template
                    echo "📋 项目目录内容："
                    ls -la
                    echo "📋 package.json 检查："
                    if [ -f package.json ]; then
                      echo "✅ package.json 存在"
                      cat package.json | head -20
                    else
                      echo "❌ package.json 不存在"
                      exit 1
                    fi
                    DEPLOY_DIR="webpage-base-template"
                  else
                    echo "❌ 未找到项目目录或 package.json"
                    exit 1
                  fi
                  
                  echo "🎯 部署目录: $DEPLOY_DIR"
                `, { language: "bash", timeoutMs: 60000 })
                
                if (prepareResult.error) {
                  write({ type: 'log', message: `❌ 部署准备失败: ${prepareResult.error}` })
                  return null
                }
                
                if (prepareResult.logs?.stdout) {
                  write({ type: 'log', message: `📄 部署准备: ${prepareResult.logs.stdout.join('')}` })
                }

                // 执行自定义服务器部署
                const deploy = await sandbox.runCode(`
                  # 智能确定工作目录
                  if [ -f "package.json" ]; then
                    echo "✅ 在项目根目录执行部署"
                    WORK_DIR="."
                  elif [ -d "webpage-base-template" ]; then
                    echo "✅ 切换到 webpage-base-template 目录"
                    cd webpage-base-template
                    WORK_DIR="webpage-base-template"
                  else
                    echo "❌ 无法找到项目目录"
                    exit 1
                  fi
                  
                  echo "📍 当前工作目录: $(pwd)"
                  
                  # 构建项目
                  echo "🔨 构建 Next.js 项目..."
                  npm run build || {
                    echo "❌ 项目构建失败"
                    exit 1
                  }
                  
                  echo "✅ 项目构建成功"
                  
                  # 创建部署包
                  echo "📦 创建部署包..."
                  DEPLOY_PACKAGE="deploy-$(date +%s).tar.gz"
                  
                  # 打包需要部署的文件
                  tar -czf "$DEPLOY_PACKAGE" \
                    .next \
                    public \
                    package.json \
                    package-lock.json \
                    next.config.* \
                    --exclude=node_modules \
                    --exclude=.git \
                    --exclude=.env.local
                  
                  echo "✅ 部署包创建完成: $DEPLOY_PACKAGE"
                  echo "📊 包大小: $(ls -lh $DEPLOY_PACKAGE | awk '{print $5}')"
                  
                  # 安装 sshpass（如果需要）
                  if ! command -v sshpass &> /dev/null; then
                    echo "📦 安装 sshpass..."
                    apt-get update && apt-get install -y sshpass || {
                      echo "❌ 无法安装 sshpass，请确保系统支持"
                      exit 1
                    }
                  fi
                  
                  # 配置 SSH 连接
                  echo "🔑 配置 SSH 连接..."
                  ssh-keyscan -H "${deploy_server}" >> ~/.ssh/known_hosts 2>/dev/null || true
                  
                  # 上传到服务器
                  echo "📤 上传到服务器 ${deploy_server}..."
                  if [ -n "${deploy_password}" ]; then
                    # 使用密码认证
                    sshpass -p "${deploy_password}" scp -o StrictHostKeyChecking=no "$DEPLOY_PACKAGE" "${deploy_user}@${deploy_server}:${deploy_path}/" || {
                      echo "❌ 文件上传失败，请检查服务器地址、用户名和密码"
                      exit 1
                    }
                  else
                    echo "❌ 未提供密码"
                    exit 1
                  fi
                  
                  echo "✅ 文件上传成功"
                  
                  # 在服务器上执行部署
                  echo "🚀 在服务器上执行部署..."
                  DEPLOY_SCRIPT="
                    cd ${deploy_path}
                    echo '📦 解压部署包...'
                    tar -xzf $DEPLOY_PACKAGE
                                     echo '📦 安装依赖...'
                     npm ci --production
                     echo '🔧 配置生产环境...'
                     export PORT=${deploy_port}
                     export NODE_ENV=production
                     cat > .env.production << PROD_ENV
PORT=${deploy_port}
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
PROD_ENV
                     echo '🔨 构建生产版本...'
                     npm run build
                     echo '🔄 重启应用...'
                     pm2 delete nextjs-app 2>/dev/null || true
                     PORT=${deploy_port} pm2 start npm --name 'nextjs-app' -- start
                     echo '✅ 部署完成'
                     echo '🌐 应用地址: http://${deploy_server}:${deploy_port}'
                  "
                  
                  if [ -n "${deploy_password}" ]; then
                    sshpass -p "${deploy_password}" ssh -o StrictHostKeyChecking=no "${deploy_user}@${deploy_server}" "$DEPLOY_SCRIPT" || {
                      echo "❌ 远程部署执行失败，请检查服务器连接和权限"
                      exit 1
                    }
                  else
                    echo "❌ 未提供密码"
                    exit 1
                  fi
                  
                  echo "🎉 部署成功完成！"
                  echo "🌐 访问地址: http://${deploy_server}:${deploy_port}"
                  
                `, { language: "bash", timeoutMs: 8 * 60 * 1000 })

                if (deploy.logs?.stdout) {
                  write({ type: 'log', message: `📄 服务器部署日志: ${deploy.logs.stdout.join('')}` })
                }
                if (deploy.logs?.stderr) {
                  write({ type: 'log', message: `❌ 服务器部署错误: ${deploy.logs.stderr.join('')}` })
                }
                
                // 检查是否是超时或终止错误
                if (deploy.error) {
                  write({ type: 'log', message: `⚠️ 服务器部署遇到问题: ${deploy.error}` })
                  return null
                }

                // 提取部署 URL
                const allLogs = (deploy.logs?.stdout || []).concat(deploy.logs?.stderr || []).join('')
                const urlMatch = allLogs.match(/访问地址: (http:\/\/[^\s]+)/);
                
                if (urlMatch) {
                  const deployUrl = urlMatch[1]
                  write({ type: 'log', message: `🎉 服务器部署成功！访问地址：${deployUrl}` })
                  return deployUrl
                } else {
                  write({ type: 'log', message: "⚠️ 服务器部署完成，但未能提取访问 URL" })
                  return `http://${deploy_server}:${deploy_port}`
                }
              }

            write({ type: 'log', message: "🚀 启动E2B Sandbox..." })
            
            // 创建 sandbox（支持 node）- 增加超时时间
            const sandbox = await Sandbox.create({ 
              apiKey: e2bApiKey,
              metadata: {
                template: "base"
              },
              timeoutMs: 20 * 60 * 1000 // 20分钟超时
            })
            
            write({ type: 'log', message: "✅ E2B Sandbox创建成功" })
            // 克隆网页基础模板项目
            write({ type: 'log', message: "📦 初始化应用项目..." })
            const cloneResult = await sandbox.runCode(`
# 克隆webpage-base-template项目
git clone https://github.com/Hehua-Fan/webpage-base-template.git

# 检查项目是否克隆成功
if [ -d "webpage-base-template" ]; then
  echo "✅ 项目克隆成功"
  cd webpage-base-template
  ls -la
  echo "📋 项目结构："
  find . -maxdepth 2 -type f -name "*.json" -o -name "*.md" | head -10
else
  echo "❌ 项目克隆失败"
  exit 1
fi
            `, { 
              language: "bash",
              timeoutMs: 600000 // 10分钟超时
            })
            
            if (cloneResult.error) {
              const errorMsg = typeof cloneResult.error === 'object' 
                ? JSON.stringify(cloneResult.error, null, 2)
                : String(cloneResult.error)
              write({ type: 'log', message: `❌ 初始化应用项目失败: ${errorMsg}` })
              if (cloneResult.logs?.stderr && cloneResult.logs.stderr.length > 0) {
                write({ type: 'log', message: `❌ 错误详情: ${cloneResult.logs.stderr.join('')}` })
              }
              write({ type: 'error', message: "Webpage base template project clone failed" })
              return
            }
            
            write({ type: 'log', message: "✅ 初始化应用项目成功" })

            // 使用AI动态生成React代码
            let generatedCode
            try {
              generatedCode = await generateReactCode()
            } catch (aiError) {
              write({ type: 'log', message: `❌ AI代码生成异常: ${aiError}` })
              generatedCode = generateFallbackCode()
            }

            write({ type: 'log', message: "📝 将AI生成的代码写入页面文件..." })
            await sandbox.runCode(`
cd webpage-base-template

# 将AI生成的代码写入app/page.tsx
cat > app/page.tsx << 'EOF'
${generatedCode}
EOF

echo "✅ AI React代码写入完成"
echo "📋 生成的代码预览："
head -30 app/page.tsx
            `, { 
              language: "bash",
              timeoutMs: 60000
            })
            
            // 配置环境变量
            write({ type: 'log', message: "🔑 配置环境变量..." })
            await sandbox.runCode(`
cd webpage-base-template

# 创建.env.local文件，添加OpenAI API密钥
cat > .env.local << 'EOF'
NEXT_TELEMETRY_DISABLED=1
OPENAI_API_KEY=${openaiApiKey}
EOF

echo "✅ 环境变量配置完成"
            `, { 
              language: "bash",
              timeoutMs: 60000
            })

            // 安装依赖
            write({ type: 'log', message: "📦 安装依赖..." })
            const installResult = await sandbox.runCode(`
cd webpage-base-template
npm install
echo "📋 依赖安装完成，检查package.json scripts："
cat package.json | grep -A 10 '"scripts"'
            `, { 
              language: "bash",
              timeoutMs: 600000 // 10分钟超时
            })
            
            if (installResult.error) {
              const errorMsg = typeof installResult.error === 'object' 
                ? JSON.stringify(installResult.error, null, 2)
                : String(installResult.error)
              write({ type: 'log', message: `❌ 依赖安装失败: ${errorMsg}` })
              if (installResult.logs?.stderr && installResult.logs.stderr.length > 0) {
                write({ type: 'log', message: `❌ 错误详情: ${installResult.logs.stderr.join('')}` })
              }
              write({ type: 'error', message: "Dependencies installation failed" })
              return
            }
            
            write({ type: 'log', message: "✅ 依赖安装成功" })

            // 根据部署类型进行部署
            let deployUrl = null
            if (deploy_type === 'server' && deploy_server && deploy_user && deploy_path) {
              try {
                deployUrl = await deployToServer(sandbox)
              } catch (deployError) {
                write({ type: 'log', message: `⚠️ 服务器部署遇到问题: ${deployError}` })
                write({ type: 'log', message: "📋 继续启动开发服务器..." })
              }
            } else if (deploy_type === 'vercel' && vercel_token) {
              try {
                deployUrl = await deployToVercel(sandbox)
              } catch (deployError) {
                write({ type: 'log', message: `⚠️ Vercel部署遇到问题: ${deployError}` })
                write({ type: 'log', message: "📋 继续启动开发服务器..." })
              }
            } else if (deploy_type !== 'none') {
              write({ type: 'log', message: "⚠️ 跳过部署：未提供完整的部署配置" })
            }

            // 启动项目（在后台）
            write({ type: 'log', message: "🌐 启动开发服务器..." })
            const startResult = await sandbox.runCode(`
cd webpage-base-template

# 清理之前的进程
pkill -f "next-server" || true
pkill -f "node.*dev" || true

# 启动Next.js开发服务器，指定主机和端口
echo "🚀 启动Next.js开发服务器..."
nohup npm run dev -- --hostname 0.0.0.0 --port 3000 > server.log 2>&1 &
echo $! > server.pid
echo "Development server started with PID: $(cat server.pid)"

# 等待服务器启动
echo "⏳ 等待服务器启动..."
sleep 10

# 检查进程是否还在运行
if [ -f server.pid ]; then
  PID=$(cat server.pid)
  if ps -p $PID > /dev/null; then
    echo "✅ 服务器进程 $PID 正在运行"
  else
    echo "❌ 服务器进程已停止"
    echo "📋 查看错误日志："
    tail -20 server.log
    exit 1
  fi
fi

# 检查端口是否开放
if netstat -tuln | grep ":3000 " > /dev/null; then
  echo "✅ 端口3000已开放"
else
  echo "❌ 端口3000未开放"
  echo "📋 查看服务器日志："
  tail -20 server.log
fi

# 测试连接
echo "🔍 测试本地连接..."
for i in {1..6}; do
if curl -f http://localhost:3000 > /dev/null 2>&1; then
  echo "✅ 服务器已成功启动在端口3000"
    break
else
    echo "⏳ 尝试 $i/6: 服务器还在启动中..."
    sleep 5
fi
done

echo "📋 最新服务器日志："
tail -15 server.log
            `, { 
              language: "bash",
              timeoutMs: 300000 // 5分钟超时
            })
            
            if (startResult.logs?.stdout) {
              write({ type: 'log', message: `📄 启动日志: ${startResult.logs.stdout.join('')}` })
            }
            if (startResult.logs?.stderr) {
              write({ type: 'log', message: `❌ 启动错误: ${startResult.logs.stderr.join('')}` })
            }

            // 获取预览链接
            write({ type: 'log', message: "🔗 获取预览链接..." })
            
            try {
              // 最终检查服务器状态
              const finalCheckResult = await sandbox.runCode(`
cd webpage-base-template

echo "🔍 最终状态检查："

# 检查进程状态
if [ -f server.pid ]; then
  PID=$(cat server.pid)
  if ps -p $PID > /dev/null; then
    echo "✅ 服务器进程 $PID 正在运行"
  else
    echo "❌ 服务器进程已停止"
    echo "📋 完整错误日志："
    cat server.log
    exit 1
  fi
else
  echo "❌ 未找到PID文件"
  exit 1
fi

# 检查端口监听状态
echo "🔍 端口监听状态："
netstat -tuln | grep 3000 || echo "端口3000未在监听"

# 检查应用健康状态
echo "🔍 应用健康检查："
timeout 10 curl -v http://localhost:3000 2>&1 || echo "连接测试失败"

# 显示完整服务器日志
echo "📋 完整服务器日志："
cat server.log | tail -30
              `, { 
                language: "bash",
                timeoutMs: 120000
              })
              
              if (finalCheckResult.logs?.stdout) {
                write({ type: 'log', message: `📄 最终检查: ${finalCheckResult.logs.stdout.join('')}` })
              }
              
              // 获取预览URL
              const host = await sandbox.getHost(3000)
              const previewUrl = `https://${host}`
              write({ type: 'log', message: `🔗 预览地址：${previewUrl}` })
              
              // 测试外部访问
              write({ type: 'log', message: "🌐 测试外部访问..." })
              const testResult = await sandbox.runCode(`
# 等待E2B代理设置完成
sleep 5

# 尝试从外部访问
echo "🔍 测试外部访问地址："
curl -I --connect-timeout 10 https://${host} 2>&1 || echo "外部访问测试完成"
              `, { 
                language: "bash",
                timeoutMs: 60000
              })
              
              if (testResult.logs?.stdout) {
                write({ type: 'log', message: `📄 外部访问测试: ${testResult.logs.stdout.join('')}` })
              }
              
              write({ type: 'log', message: "✅ AI React项目已启动，预览地址已生成" })
              
              // 在成功响应中包含预览URL和部署URL
              const successMessage = deployUrl 
                ? `项目已成功构建！沙盒预览：${previewUrl}，生产环境：${deployUrl}`
                : `项目已成功构建和启动，AI已根据需求生成自定义React代码`
              
              write({ 
                type: 'success', 
                message: successMessage,
                project_name: "webpage-base-template",
                preview_url: previewUrl,
                deploy_url: deployUrl
              })
              return
              
            } catch (error) {
              write({ type: 'log', message: `⚠️ 获取预览链接时出现问题: ${error}` })
              
              // 显示详细的错误诊断
              await sandbox.runCode(`
cd webpage-base-template
echo "❌ 错误诊断信息："
echo "📋 进程状态："
ps aux | grep node | head -5
echo "📋 端口状态："
netstat -tuln | grep 3000
echo "📋 服务器日志（最后50行）："
tail -50 server.log
              `, { 
                language: "bash",
                timeoutMs: 60000
              })
            }
            
            write({ 
              type: 'success', 
              message: `网页基础模板项目已在sandbox中成功构建和启动，AI已根据需求生成自定义React代码`,
              project_name: "webpage-base-template"
            })

                     } catch (err: unknown) {
             write({ type: 'error', message: String(err) })
          } finally {
            finish()                // ❗ 只在这里关闭
          }
        })()
      },

      /** 若浏览器中途取消，Runtime 会触发 cancel */
      cancel() {
        closed = true
      }
    })

    return new NextResponse(stream, {
      headers: { 
        'Content-Type': 'text/event-stream; charset=utf-8',
        'Transfer-Encoding': 'chunked',
      }
    })

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return NextResponse.json(
      { 
        success: false, 
        error: errorMessage,
        logs: [`❌ 请求处理失败: ${errorMessage}`]
      },
      { status: 500 }
    )
  }
}
