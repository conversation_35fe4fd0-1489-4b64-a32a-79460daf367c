'use client'

import React, { useState } from 'react'
import MarkdownRenderer from '@/lib/Renderer/MarkdownRenderer'
import MermaidRenderer from '@/lib/Renderer/MermaidRenderer'
import Sidebar from '@/components/Sidebar'
import { Button } from '@/components/ui/button'
import { Eye, Code } from 'lucide-react'
import CodeViewRenderer from '@/lib/Renderer/CodeViewRenderer'

const testMermaidContent = `
# Mermaid图表渲染测试

这是一个测试页面，用于验证Mermaid图表渲染功能。

## 流程图示例

\`\`\`mermaid
graph TD
    A[数据输入] --> B[数据处理]
    B --> C[特征提取]
    C --> D[模型训练]
    D --> E[模型评估]
    E --> F{是否满足要求？}
    F -->|是| G[部署模型]
    F -->|否| H[调整参数]
    H --> D
    G --> I[线上服务]
\`\`\`

## 时序图示例

\`\`\`mermaid
sequenceDiagram
    participant U as 用户
    participant C as 客户端
    participant S as 服务器
    participant D as 数据库

    U->>C: 发送请求
    C->>S: API调用
    S->>D: 查询数据
    D-->>S: 返回结果
    S-->>C: 响应数据
    C-->>U: 显示结果
\`\`\`

## 类图示例

\`\`\`mermaid
classDiagram
    class LLMService {
        +String model
        +String apiKey
        +generateResponse()
        +processMessage()
    }
    class MCPManager {
        +List servers
        +connectServer()
        +executeAction()
    }
    class ChatInterface {
        +List messages
        +sendMessage()
        +renderContent()
    }
    
    LLMService --> MCPManager
    ChatInterface --> LLMService
\`\`\`

## 状态图示例

\`\`\`mermaid
stateDiagram-v2
    [*] --> 空闲
    空闲 --> 处理中: 接收消息
    处理中 --> 调用工具: 需要工具
    处理中 --> 生成回复: 直接回复
    调用工具 --> 生成回复: 工具执行完成
    生成回复 --> 空闲: 回复完成
    生成回复 --> [*]: 会话结束
\`\`\`
`

const mermaidExamples = {
  flowchart: {
    title: "流程图",
    code: `graph TD
    A["数据输入"] --> B["数据处理"]
    B --> C["特征提取"]
    C --> D["模型训练"]
    D --> E["模型评估"]
    E --> F{"是否满足要求？"}
    F -->|是| G["部署模型"]
    F -->|否| H["调整参数"]
    H --> D
    G --> I["线上服务"]`
  },
  sequence: {
    title: "时序图",
    code: `sequenceDiagram
    participant U as 用户
    participant C as 客户端
    participant S as 服务器
    participant D as 数据库

    U->>C: 发送请求
    C->>S: API调用
    S->>D: 查询数据
    D-->>S: 返回结果
    S-->>C: 响应数据
    C-->>U: 显示结果`
  },
  class: {
    title: "类图",
    code: `classDiagram
    class LLMService {
        +String model
        +String apiKey
        +generateResponse()
        +processMessage()
    }
    class MCPManager {
        +List servers
        +connectServer()
        +executeAction()
    }
    class ChatInterface {
        +List messages
        +sendMessage()
        +renderContent()
    }
    
    LLMService --> MCPManager
    ChatInterface --> LLMService`
  },
  state: {
    title: "状态图",
    code: `stateDiagram-v2
    [*] --> 空闲
    空闲 --> 处理中: 接收消息
    处理中 --> 调用工具: 需要工具
    处理中 --> 生成回复: 直接回复
    调用工具 --> 生成回复: 工具执行完成
    生成回复 --> 空闲: 回复完成
    生成回复 --> [*]: 会话结束`
  }
}

export default function TestMermaidPage() {
  const [customContent] = useState(testMermaidContent)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true)
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('preview')
  const [selectedExample, setSelectedExample] = useState<keyof typeof mermaidExamples>('flowchart')
  const [currentMermaidCode, setCurrentMermaidCode] = useState(mermaidExamples.flowchart.code)

  const handleExampleChange = (exampleKey: keyof typeof mermaidExamples) => {
    setSelectedExample(exampleKey)
    setCurrentMermaidCode(mermaidExamples[exampleKey].code)
  }


  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar 
        isCollapsed={sidebarCollapsed}
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
      />
      
      <div className={`flex-1 transition-all duration-300 ease-in-out ${
        sidebarCollapsed ? 'ml-0' : 'ml-0'
      }`}>
        <div className="container mx-auto p-8 max-w-6xl">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold text-gray-900">
          🎨 Mermaid 图表测试中心
        </h1>
      </div>
      
      <div className="grid lg:grid-cols-1 gap-8">
        <div className="space-y-6">
          {/* Interactive Mermaid Demo */}
          <div className="bg-white rounded-lg border">
            {/* Header with Tab Controls */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <h3 className="text-lg font-semibold">交互式 Mermaid 演示</h3>
                <span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">
                  {mermaidExamples[selectedExample].title}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="flex items-center bg-gray-100 rounded-lg p-1">
                  <Button
                    variant={viewMode === 'preview' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('preview')}
                    className="px-3 py-1 h-8"
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    预览
                  </Button>
                  <Button
                    variant={viewMode === 'code' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('code')}
                    className="px-3 py-1 h-8"
                  >
                    <Code className="w-4 h-4 mr-1" />
                    代码
                  </Button>
                </div>
              </div>
            </div>
            
            {/* Example Selector */}
            <div className="p-3 border-b border-gray-200">
              <div className="flex space-x-2 overflow-x-auto">
                {Object.entries(mermaidExamples).map(([key, example]) => (
                  <button
                    key={key}
                    onClick={() => handleExampleChange(key as keyof typeof mermaidExamples)}
                    className={`px-3 py-2 text-sm rounded-lg whitespace-nowrap transition-colors ${
                      selectedExample === key
                        ? 'bg-blue-100 text-blue-700 border border-blue-200'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {example.title}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Content */}
            <div className="min-h-[500px] max-h-[2000px] overflow-auto">
              {viewMode === 'preview' ? (
                <div className="h-full p-4">
                                      <MermaidRenderer chart={currentMermaidCode} />
                </div>
              ) : (
                <CodeViewRenderer 
                  code={currentMermaidCode}
                  language="mermaid"
                />
              )}
            </div>
          </div>
          
          {/* Original Markdown Demo */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-xl font-semibold mb-4">Markdown 渲染测试</h2>
            <MarkdownRenderer content={customContent} />
          </div>
        </div>
      </div>
      
      <div className="mt-8 p-4 bg-green-50 border border-green-200 rounded-lg">
        <h3 className="font-medium text-green-900 mb-2">✅ 功能状态</h3>
        <ul className="text-sm text-green-800 space-y-1">
          <li>• Mermaid 依赖已安装：mermaid@11.7.0</li>
          <li>• MarkdownRenderer 组件已集成 MermaidRenderer</li>
          <li>• 支持所有主要 Mermaid 图表类型</li>
          <li>• 错误处理和加载状态已实现</li>
          <li>• 响应式设计和样式优化完成</li>
        </ul>
      </div>
      
      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-medium text-blue-900 mb-2">🚀 在聊天界面中使用</h3>
        <p className="text-sm text-blue-800 mb-2">
          现在你可以在聊天界面中直接使用Mermaid图表了！试试这些指令：
        </p>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• &quot;帮我画一个用户登录的流程图&quot;</li>
          <li>• &quot;生成一个数据库设计的ER图&quot;</li>
          <li>• &quot;创建一个项目开发的甘特图&quot;</li>
          <li>• 或者直接粘贴包含 mermaid 代码块的内容</li>
        </ul>
      </div>
        </div>
      </div>
    </div>
  )
} 