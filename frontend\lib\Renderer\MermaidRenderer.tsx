'use client'

import React, { useEffect, useRef, useState } from 'react'

const MermaidRenderer: React.FC<{ chart: string }> = ({ chart }) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [status, setStatus] = useState('准备渲染...')
  const [svg, setSvg] = useState<string>('')
  const [error, setError] = useState<string>('')

  useEffect(() => {
    const renderMermaid = async () => {
      try {
        // 只在客户端运行
        if (typeof window === 'undefined') {
          setStatus('等待客户端加载...')
          return
        }

        setStatus('检查参数...')
        if (!chart || !chart.trim()) {
          throw new Error('图表内容为空')
        }

        setStatus('动态导入mermaid...')
        const mermaid = (await import('mermaid')).default

        setStatus('配置mermaid...')
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'system-ui, sans-serif',
          fontSize: 16,
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
          },
          themeVariables: {
            // 去掉决策分支标签的灰色背景
            edgeLabelBackground: 'transparent',
            // 保持其他颜色不变
            primaryColor: '#fff2cc',
            primaryTextColor: '#000000',
            primaryBorderColor: '#d6b656',
            lineColor: '#666666',
            tertiaryColor: '#fff'
          }
        })

        setStatus('清理图表语法...')
        const cleanChart = chart
          .replace(/\\n/g, '\n')  // 转换字符串换行符
          .trim()

        setStatus('验证图表语法...')
        console.log('Chart to render:', cleanChart)

        setStatus('渲染图表...')
        const uniqueId = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`
        
        const result = await mermaid.render(uniqueId, cleanChart)
        
        // 优化SVG样式 - 确保居中和比例不变
        const optimizedSvg = result.svg
          .replace(/<svg([^>]*)>/, '<svg$1 style="display: block; margin: 0 auto; max-width: 100%; height: auto;">')
        
        setSvg(optimizedSvg)
        setStatus('渲染成功')

      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '未知错误'
        setError(errorMsg)
        setStatus(`渲染失败: ${errorMsg}`)
        console.error('MermaidRenderer error:', err)
      }
    }

    const timeoutId = setTimeout(renderMermaid, 1000)
    return () => clearTimeout(timeoutId)
  }, [chart])

  if (error) {
    return (
      <div style={{ 
        border: '2px solid #ef4444', 
        borderRadius: '8px', 
        padding: '16px', 
        backgroundColor: '#fef2f2',
        color: '#dc2626'
      }}>
        <h4>❌ 渲染失败</h4>
        <p><strong>错误:</strong> {error}</p>
        <details style={{ marginTop: '8px' }}>
          <summary style={{ cursor: 'pointer' }}>显示图表代码</summary>
          <pre style={{ 
            backgroundColor: '#fee2e2', 
            padding: '8px', 
            borderRadius: '4px', 
            fontSize: '12px',
            overflow: 'auto',
            marginTop: '8px'
          }}>
            {chart}
          </pre>
        </details>
      </div>
    )
  }

  return (
    <div style={{ 
      padding: '16px',
      backgroundColor: 'white'
    }}>
      <p style={{ marginBottom: '12px', fontSize: '14px', color: '#059669' }}>
      </p>
      
      {svg ? (
        <div 
          ref={containerRef}
          style={{ 
            border: '1px solid #d1d5db', 
            borderRadius: '4px', 
            padding: '20px', 
            backgroundColor: 'white',
            textAlign: 'center',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '200px'
          }}
          dangerouslySetInnerHTML={{ __html: svg }}
        />
      ) : (
        <div style={{ 
          border: '1px dashed #9ca3af', 
          borderRadius: '4px', 
          padding: '32px', 
          textAlign: 'center',
          backgroundColor: '#f9fafb',
          color: '#6b7280'
        }}>
          {status.includes('渲染') ? (
            <div>
              <div style={{ 
                display: 'inline-block', 
                width: '20px', 
                height: '20px', 
                border: '2px solid #3b82f6',
                borderTopColor: 'transparent',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }} />
              <p style={{ marginTop: '8px' }}>{status}</p>
            </div>
          ) : (
            <p>{status}</p>
          )}
        </div>
      )}
    </div>
  )
}

export default MermaidRenderer 