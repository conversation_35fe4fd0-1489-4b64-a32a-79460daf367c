'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Loader2, Code, CheckCircle, Sparkles, Clock } from 'lucide-react'
import Image from 'next/image'
import toast from 'react-hot-toast'
import Sidebar from '@/components/Sidebar'

interface GenerationResult {
  success: boolean
  logs: string[]
  project_name?: string
  preview_url?: string
  error?: string
}

const LogDisplay = ({ logs }: { logs: string[] }) => (
  <div className="bg-gray-50 text-black p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto border border-gray-200">
    {logs.map((log, index) => (
      <div key={index} className="mb-1">
        {log}
      </div>
    ))}
  </div>
)

export default function TestWebpagePage() {
  // 模式切换状态
  const [mode, setMode] = useState<'detailed' | 'simple'>('detailed')
  
  // 详细模式的状态
  const [pageTitle, setPageTitle] = useState('智能宠物管家')
  const [description, setDescription] = useState('一个帮助您管理宠物健康、喂食和玩耍的智能应用')
  const [requirements, setRequirements] = useState('包含宠物信息管理、健康记录、喂食提醒等功能')
  const [style, setStyle] = useState('现代简洁')
  const [features] = useState(['响应式设计', '交互式组件', '数据统计', '通知提醒'])
  
  // 简单模式的状态
  const [simpleQuery, setSimpleQuery] = useState('创建一个现代化的宠物管理系统，包含宠物信息展示、健康监控、喂食记录等功能，使用温馨的设计风格')
  
  // 部署配置
  const [deployType, setDeployType] = useState<'none' | 'server' | 'vercel'>('none')
  
  // 自定义服务器部署配置
  const [deployServer, setDeployServer] = useState('')
  const [deployUser, setDeployUser] = useState('')
  const [deployPassword, setDeployPassword] = useState('')
  const [deployPath, setDeployPath] = useState('')
  const [deployPort, setDeployPort] = useState('3000')
  
  // Vercel部署配置
  const [vercelToken, setVercelToken] = useState('')
  const [vercelOrgId, setVercelOrgId] = useState('')
  
  const [isGenerating, setIsGenerating] = useState(false)
  const [result, setResult] = useState<GenerationResult | null>(null)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true)

  // 计时器相关状态
  const [startTime, setStartTime] = useState<Date | null>(null)
  const [endTime, setEndTime] = useState<Date | null>(null)
  const [elapsedTime, setElapsedTime] = useState(0)

  // 将features数组转换为字符串，便于编辑
  const [featuresText, setFeaturesText] = useState(features.join(', '))

  // 计时器效果
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null
    
    if (isGenerating && startTime) {
      interval = setInterval(() => {
        setElapsedTime(Date.now() - startTime.getTime())
      }, 100) // 每100ms更新一次
    } else if (!isGenerating && interval) {
      clearInterval(interval)
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isGenerating, startTime])

  // 格式化时间显示
  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    const milliseconds = Math.floor((ms % 1000) / 10)
    
    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(2, '0')}`
    } else {
      return `${remainingSeconds}.${milliseconds.toString().padStart(2, '0')}s`
    }
  }

  // 获取总用时
  const getTotalTime = () => {
    if (endTime && startTime) {
      return endTime.getTime() - startTime.getTime()
    } else if (isGenerating && startTime) {
      return elapsedTime
    }
    return 0
  }

  const handleGenerate = async () => {
    // 验证输入
    if (mode === 'detailed') {
      if (!pageTitle.trim()) {
        toast.error('请输入页面标题')
        return
      }
      if (!description.trim()) {
        toast.error('请输入页面描述')
        return
      }
    } else {
      if (!simpleQuery.trim()) {
        toast.error('请输入查询内容')
        return
      }
    }
    
    // 验证部署配置
    if ((deployType as string) === 'vercel') {
      toast.error('Vercel部署功能即将推出，请选择其他部署方式')
      return
    }
    
    if (deployType === 'server') {
      if (!deployServer.trim()) {
        toast.error('请输入服务器地址')
        return
      }
      if (!deployUser.trim()) {
        toast.error('请输入用户名')
        return
      }
      if (!deployPassword.trim()) {
        toast.error('请输入SSH密码')
        return
      }
      if (!deployPath.trim()) {
        toast.error('请输入部署路径')
        return
      }
      if (!deployPort.trim() || isNaN(Number(deployPort)) || Number(deployPort) < 1 || Number(deployPort) > 65535) {
        toast.error('请输入有效的端口号 (1-65535)')
        return
      }
    }

    setIsGenerating(true)
    setResult({ success: false, logs: [] })
    
    // 启动计时器
    const startTime = new Date()
    setStartTime(startTime)
    setEndTime(null)

    try {
      let requestBody
      
      if (mode === 'detailed') {
        // 详细模式：发送具体的参数
        const featuresArray = featuresText.split(',').map(f => f.trim()).filter(f => f.length > 0)
        requestBody = {
          mode: 'detailed',
          page_title: pageTitle,
          description: description,
          requirements: requirements,
          style: style,
          features: featuresArray,
          deploy_type: deployType,
          deploy_server: deployType === 'server' ? deployServer : '',
          deploy_user: deployType === 'server' ? deployUser : '',
          deploy_password: deployType === 'server' ? deployPassword : '',
          deploy_path: deployType === 'server' ? deployPath : '',
          deploy_port: deployType === 'server' ? deployPort : '3000',
          vercel_token: (deployType as string) === 'vercel' ? vercelToken : '',
          vercel_org_id: (deployType as string) === 'vercel' ? vercelOrgId : '',
        }
      } else {
        // 简单模式：只发送查询内容
        requestBody = {
          mode: 'simple',
          query: simpleQuery,
          deploy_type: deployType,
          deploy_server: deployType === 'server' ? deployServer : '',
          deploy_user: deployType === 'server' ? deployUser : '',
          deploy_password: deployType === 'server' ? deployPassword : '',
          deploy_path: deployType === 'server' ? deployPath : '',
          deploy_port: deployType === 'server' ? deployPort : '3000',
          vercel_token: (deployType as string) === 'vercel' ? vercelToken : '',
          vercel_org_id: (deployType as string) === 'vercel' ? vercelOrgId : '',
        }
      }
      
      const response = await fetch('/api/webpage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || 'Generation failed')
      }

      // 处理流式响应
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()
      
      if (!reader) {
        throw new Error('无法读取响应流')
      }

      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break
        
        const text = decoder.decode(value, { stream: true })
        const lines = text.split('\n').filter(line => line.trim())
        
        for (const line of lines) {
          try {
            const data = JSON.parse(line)
            
            if (data.type === 'log') {
              setResult(prev => ({
                ...prev!,
                logs: [...(prev?.logs || []), data.message]
              }))
            } else if (data.type === 'error') {
              setResult(prev => ({
                success: false,
                logs: [...(prev?.logs || []), data.message],
                error: data.message
              }))
              toast.error('项目生成失败')
              break
            } else if (data.type === 'success') {
              setResult(prev => ({
                success: true,
                logs: [...(prev?.logs || []), data.message],
                project_name: data.project_name,
                preview_url: data.preview_url
              }))
              toast.success('项目生成成功!')
              break
            }
          } catch {
            console.warn('解析响应行失败:', line)
          }
        }
      }
    } catch (error) {
      console.error('Generation error:', error)
      toast.error(`生成失败: ${error instanceof Error ? error.message : '未知错误'}`)
      setResult({
        success: false,
        logs: [`❌ 错误: ${error instanceof Error ? error.message : '未知错误'}`],
        error: error instanceof Error ? error.message : '未知错误'
      })
    } finally {
      setIsGenerating(false)
      // 停止计时器
      setEndTime(new Date())
    }
  }

  return (
    <div className="flex h-screen bg-white">
      {/* Sidebar */}
      <Sidebar 
        isCollapsed={sidebarCollapsed} 
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)} 
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0 overflow-hidden">
        <div className="flex-1 overflow-auto p-8">
          <div className="max-w-8xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 左侧区域 */}
              <div className="space-y-6">
                                {/* 配置区域 */}
                <Card className="border-gray-200 shadow-sm">
                  <CardHeader>
                    {/* Tab 切换 */}
                    <div className="flex gap-1 mt-4 p-1 bg-gray-50 rounded-lg">
                                          <button
                        onClick={() => setMode('detailed')}
                        className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded text-sm font-medium transition-all ${
                          mode === 'detailed'
                            ? 'bg-white text-black shadow-sm border border-gray-200'
                            : 'text-gray-600 hover:text-black'
                        }`}
                      >
                        <Image src="/form.png" alt="表单" width={16} height={16} className="w-4 h-4" />
                        一表单生成应用
                      </button>
                      <button
                        onClick={() => setMode('simple')}
                        className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded text-sm font-medium transition-all ${
                          mode === 'simple'
                            ? 'bg-white text-black shadow-sm border border-gray-200'
                            : 'text-gray-600 hover:text-black'
                        }`}
                      >
                        <Image src="/message.png" alt="消息" width={16} height={16} className="w-4 h-4" />
                        一句话生成应用
                      </button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  {mode === 'detailed' ? (
                    <>
                      {/* 详细配置模式 */}
                      {/* 页面标题 */}
                      <div>
                        <Label htmlFor="pageTitle">页面标题 *</Label>
                        <Input
                          id="pageTitle"
                          placeholder="例如: 智能宠物管家"
                          value={pageTitle}
                          onChange={(e) => setPageTitle(e.target.value)}
                          className="mt-1"
                        />
                      </div>

                      {/* 页面描述 */}
                      <div>
                        <Label htmlFor="description">页面描述 *</Label>
                        <Input
                          id="description"
                          placeholder="例如: 一个帮助您管理宠物的智能应用"
                          value={description}
                          onChange={(e) => setDescription(e.target.value)}
                          className="mt-1"
                        />
                      </div>

                      {/* 具体要求 */}
                      <div>
                        <Label htmlFor="requirements">具体要求</Label>
                        <Input
                          id="requirements"
                          placeholder="例如: 包含用户登录、数据展示、表单提交等功能"
                          value={requirements}
                          onChange={(e) => setRequirements(e.target.value)}
                          className="mt-1"
                        />
                      </div>

                      {/* 设计风格 */}
                      <div>
                        <Label htmlFor="style">设计风格</Label>
                        <Input
                          id="style"
                          placeholder="例如: 现代简洁、科技感、温馨可爱"
                          value={style}
                          onChange={(e) => setStyle(e.target.value)}
                          className="mt-1"
                        />
                      </div>

                      {/* 功能特性 */}
                      <div>
                        <Label htmlFor="features">功能特性</Label>
                        <Input
                          id="features"
                          placeholder="用逗号分隔，例如: 响应式设计, 交互式组件, 数据统计"
                          value={featuresText}
                          onChange={(e) => setFeaturesText(e.target.value)}
                          className="mt-1"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          多个特性请用逗号分隔
                        </p>
                      </div>
                    </>
                  ) : (
                    <>
                      {/* 简单查询模式 */}
                      <div>
                        <Label htmlFor="simpleQuery">描述您想要的网站 *</Label>
                        <textarea
                          id="simpleQuery"
                          placeholder="例如: 创建一个现代化的宠物管理系统，包含宠物信息展示、健康监控、喂食记录等功能，使用温馨的设计风格"
                          value={simpleQuery}
                          onChange={(e) => setSimpleQuery(e.target.value)}
                          className="mt-1 w-full min-h-[120px] p-3 border border-gray-300 rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                          rows={5}
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          详细描述您的需求，AI 将自动为您设计和开发
                        </p>
                      </div>
                    </>
                  )}

                  {/* 部署配置 */}
                  <div className="border-t pt-6">
                    <div className="mb-4">
                      <Label className="text-sm font-medium mb-3 block">🚀 部署选项</Label>
                      <RadioGroup 
                        value={deployType} 
                        onValueChange={(value) => {
                          // 暂时禁用Vercel选项，但仍然允许设置以避免类型错误
                          setDeployType(value as 'none' | 'server' | 'vercel')
                        }}
                        className="space-y-2"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="none" id="deployNone" />
                          <Label htmlFor="deployNone" className="text-sm font-normal cursor-pointer">
                            仅沙盒预览（只有2分钟的预览时间）
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="server" id="deployServer" />
                          <Label htmlFor="deployServer" className="text-sm font-normal cursor-pointer">
                            部署到自定义服务器（可长久预览）
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2 opacity-50">
                          <RadioGroupItem value="vercel" id="deployVercel" disabled />
                          <Label htmlFor="deployVercel" className="text-sm font-normal cursor-not-allowed text-gray-400">
                            部署到 Vercel （即将推出）
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>
                    
                    {deployType === 'server' && (
                      <div className="space-y-4 pl-6 border-l-2 border-gray-100">
                        <div>
                          <Label htmlFor="deployServer">服务器地址 *</Label>
                          <Input
                            id="deployServer"
                            placeholder="例如: ************* 或 example.com"
                            value={deployServer}
                            onChange={(e) => setDeployServer(e.target.value)}
                            className="mt-1"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            服务器的 IP 地址或域名
                          </p>
                        </div>
                        
                        <div>
                          <Label htmlFor="deployUser">用户名 *</Label>
                          <Input
                            id="deployUser"
                            placeholder="例如: root 或 ubuntu"
                            value={deployUser}
                            onChange={(e) => setDeployUser(e.target.value)}
                            className="mt-1"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            SSH 登录用户名
                          </p>
                        </div>
                        
                        <div>
                          <Label htmlFor="deployPassword">SSH 密码 *</Label>
                          <Input
                            id="deployPassword"
                            type="password"
                            placeholder="输入SSH登录密码"
                            value={deployPassword}
                            onChange={(e) => setDeployPassword(e.target.value)}
                            className="mt-1"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            SSH 登录密码，用于连接服务器
                          </p>
                        </div>
                        
                        <div>
                          <Label htmlFor="deployPath">部署路径 *</Label>
                          <Input
                            id="deployPath"
                            placeholder="例如: /var/www/myapp"
                            value={deployPath}
                            onChange={(e) => setDeployPath(e.target.value)}
                            className="mt-1"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            服务器上的部署目录路径
                          </p>
                        </div>
                        
                        <div>
                          <Label htmlFor="deployPort">端口号 *</Label>
                          <Input
                            id="deployPort"
                            placeholder="例如: 3000"
                            value={deployPort}
                            onChange={(e) => setDeployPort(e.target.value)}
                            className="mt-1"
                            type="number"
                            min="1"
                            max="65535"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            应用运行的端口号 (1-65535)
                          </p>
                        </div>
                        
                        <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                          <p className="text-xs text-blue-700">
                            💡 <strong>部署要求：</strong>
                          </p>
                          <ul className="text-xs text-blue-700 mt-1 space-y-1 list-disc list-inside">
                            <li>服务器需要安装 Node.js 和 npm</li>
                            <li>建议安装 PM2 进程管理器</li>
                            <li>确保部署路径有写入权限</li>
                            <li>服务器需要支持 SSH 密码认证</li>
                            <li>项目将在指定端口上运行</li>
                          </ul>
                        </div>
                      </div>
                    )}
                    
                    {deployType === 'vercel' && (
                      <div className="space-y-4 pl-6 border-l-2 border-gray-100">
                        <div>
                          <Label htmlFor="vercelToken">Vercel Token *</Label>
                          <Input
                            id="vercelToken"
                            type="password"
                            placeholder="输入您的Vercel Token"
                            value={vercelToken}
                            onChange={(e) => setVercelToken(e.target.value)}
                            className="mt-1"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            在 <a href="https://vercel.com/account/tokens" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Vercel设置页面</a> 创建访问令牌
                          </p>
                        </div>
                        
                        <div>
                          <Label htmlFor="vercelOrgId">组织ID (可选)</Label>
                          <Input
                            id="vercelOrgId"
                            placeholder="例如: team_abc123 (个人账户可留空)"
                            value={vercelOrgId}
                            onChange={(e) => setVercelOrgId(e.target.value)}
                            className="mt-1"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            仅在部署到团队/组织账户时需要，个人账户可留空
                          </p>
                        </div>
                        
                        <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                          <p className="text-xs text-green-700">
                            💡 <strong>Vercel部署优势：</strong>
                          </p>
                          <ul className="text-xs text-green-700 mt-1 space-y-1 list-disc list-inside">
                            <li>自动获得HTTPS域名</li>
                            <li>全球CDN加速</li>
                            <li>自动部署优化</li>
                            <li>无需服务器维护</li>
                            <li>支持自定义域名</li>
                          </ul>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex gap-3">
                    <Button
                      onClick={handleGenerate}
                      disabled={
                        isGenerating || 
                        (mode === 'detailed' && (!pageTitle.trim() || !description.trim())) ||
                        (mode === 'simple' && !simpleQuery.trim()) ||
                        (deployType === 'server' && (!deployServer.trim() || !deployUser.trim() || !deployPassword.trim() || !deployPath.trim() || !deployPort.trim() || isNaN(Number(deployPort)) || Number(deployPort) < 1 || Number(deployPort) > 65535)) ||
                        ((deployType as string) === 'vercel') // 暂时禁用Vercel选项
                      }
                      className="flex-1 bg-black hover:bg-gray-800 text-white"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          AI 生成中...
                        </>
                      ) : (
                        <>
                          <Sparkles className="w-4 h-4 mr-2" />
                          {mode === 'detailed' ? '生成 React 代码' : '智能生成代码'}
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
                </Card>

                {/* 预览地址显示 - 移到左侧下方 */}
                {result?.success && result?.preview_url && (
                  <Card className="bg-white border-gray-200 shadow-sm">
                    <CardContent className="p-6">
                      <div className="text-center space-y-4">
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <Sparkles className="w-4 h-4 text-black" />
                          </div>
                          <h3 className="text-lg font-semibold text-black">🎉 预览您的 AI 生成网站</h3>
                        </div>
                        
                        <p className="text-sm text-gray-600">
                          您的个性化 React 应用已准备就绪！点击下方链接即可访问
                        </p>
                        
                        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                          <div className="flex items-center gap-3">
                            <div className="flex-1 text-left">
                              <p className="text-xs text-gray-500 mb-1">预览地址</p>
                              <p className="text-sm font-mono text-gray-800 break-all">
                                {result.preview_url}
                              </p>
                            </div>
                            <Button
                              onClick={() => window.open(result.preview_url, '_blank')}
                              className="bg-black hover:bg-gray-800 text-white px-6"
                            >
                              <Code className="w-4 h-4 mr-2" />
                              访问网站
                            </Button>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-center gap-4 text-xs text-gray-600">
                          <span>✨ AI 智能生成</span>
                          <span>•</span>
                          <span>🚀 实时部署</span>
                          <span>•</span>
                          <span>📱 响应式设计</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* 结果展示区域 */}
              <div className="space-y-4">
                <Card className="border-gray-200 shadow-sm">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Image src="/computer.png" alt="电脑" width={20} height={20} className="w-5 h-5" />
                        Zenus&apos;s Computer
                      </div>
                      {(isGenerating || startTime) && (
                        <div className="flex items-center gap-2 text-sm">
                          <Clock className="w-4 h-4 text-gray-600" />
                          <span className="font-mono font-bold text-black">
                            {formatTime(getTotalTime())}
                          </span>
                        </div>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {!result && !isGenerating && (
                      <div className="text-center py-12">
                        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-lg flex items-center justify-center">
                          <Sparkles className="w-8 h-8 text-black" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">准备开始 AI 生成</h3>
                        <p className="text-gray-500">
                          配置您的需求后点击生成按钮，Agent将为您创建个性化的 React 组件
                        </p>
                      </div>
                    )}

                    {(isGenerating || (result && result.logs && result.logs.length > 0)) && (
                      <>
                        {isGenerating && result && result.logs && result.logs.length === 0 && (
                          <div className="text-center py-12">
                            <div className="relative">
                              <Loader2 className="w-16 h-16 mx-auto mb-4 animate-spin text-black" />
                              <Sparkles className="w-6 h-6 absolute top-1 left-1/2 transform -translate-x-1/2 text-gray-600 animate-pulse" />
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">AI 正在生成代码...</h3>
                            <p className="text-gray-500">
                              正在执行：AI 分析需求 → 生成 React 代码 → 部署到沙盒
                            </p>
                          </div>
                        )}
                        
                        {result && result.logs && result.logs.length > 0 && (
                          <LogDisplay logs={result.logs} />
                        )}
                      </>
                    )}

                    {result?.success && (
                      <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center gap-2 text-green-800">
                          <CheckCircle className="w-5 h-5" />
                          <span className="font-medium">AI 代码生成成功!</span>
                        </div>
                        <p className="text-sm text-green-700 mt-1">
                          项目 &quot;{result.project_name}&quot; 已成功生成，React 组件已部署到沙盒环境。
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
