from typing import List, Optional, Literal
from pydantic import BaseModel


class ChatMessage(BaseModel):
    """聊天消息"""
    role: Literal['user', 'assistant']
    content: str
    timestamp: Optional[int] = None


class ChatRequest(BaseModel):
    """聊天请求数据类"""
    message: str
    mcp_servers: List[str]
    model: str = "openai-gpt-4o"
    chat_history: Optional[List[ChatMessage]] = []  # 添加聊天历史


class PlanStep(BaseModel):
    """执行计划步骤"""
    step: int
    action: Literal['call_mcp', 'direct_response', 'summary']
    server: Optional[str] = None
    description: str
    status: Optional[Literal['pending', 'executing', 'completed', 'skipped']] = 'pending'


class ExecutionPlan(BaseModel):
    """执行计划"""
    use_mcp: Literal['yes', 'no']
    reason: str
    plan: List[PlanStep]


class ExecutePlanRequest(BaseModel):
    """执行计划请求数据类"""
    plan: ExecutionPlan
    mcp_servers: List[str]
    model: str = "openai-gpt-4o"
    original_message: Optional[str] = None  # 添加原始用户消息
    chat_history: Optional[List[ChatMessage]] = []  # 添加聊天历史


class ModelConfig(BaseModel):
    """模型配置数据类"""
    provider: str
    model_name: str
    model_display_name: str
    model_version: Optional[str] = None
    api_key: str
    base_url: Optional[str] = None


class SandboxAnalyzeRequest(BaseModel):
    """Sandbox分析请求数据类"""
    analysis_request: str
    model: str = "gpt-4.1"  # 使用可用的模型
    file_content: bytes = None  # 用于内部传递，不在API中直接使用


class WebpageGenerateRequest(BaseModel):
    """网页生成请求数据类"""
    project_name: str = "ai-nextjs-project"
    page_title: str = "欢迎来到宠物之家"
    button_text: str = "了解更多"