'use client'

import { useState, useEffect } from 'react'
import { Menu } from 'lucide-react'
import ChatInterface from '@/components/chat-interface'
import Sidebar from '@/components/Sidebar'

export default function Home() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true)
  const [isMobile, setIsMobile] = useState(false)

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth <= 768
      setIsMobile(mobile)
      // 移动端默认折叠侧边栏
      if (mobile) {
        setSidebarCollapsed(true)
      }
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const handleNavigate = (itemId: string) => {
    // 导航逻辑已移至 Sidebar 组件处理
    console.log('Navigating to:', itemId)
  }

  return (
    <div className="flex h-screen overflow-hidden relative">
      {/* 侧边栏 */}
      <Sidebar 
        isCollapsed={sidebarCollapsed} 
        onToggle={handleToggleSidebar}
        onNavigate={handleNavigate}
      />
      
      {/* 移动端汉堡菜单按钮 */}
      {isMobile && sidebarCollapsed && (
        <button
          onClick={handleToggleSidebar}
          className="fixed top-4 left-4 z-30 p-2 bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 border border-gray-200"
        >
          <Menu className="w-5 h-5 text-gray-600" />
        </button>
      )}
      
      {/* 主内容区域 */}
      <div className="flex-1 overflow-hidden">
        <ChatInterface sidebarCollapsed={sidebarCollapsed} />
      </div>
    </div>
  )
}
