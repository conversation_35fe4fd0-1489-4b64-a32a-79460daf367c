'use client'

import React from 'react'

interface CodeBlockRendererProps {
  language?: string
  value: string
  title?: string
}

const CodeBlockRenderer: React.FC<CodeBlockRendererProps> = ({ language, value, title }) => {
  return (
    <div className="my-4 overflow-hidden rounded-md border border-gray-200 max-w-full">
      {title && (
        <div className="bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 border-b border-gray-200 break-words">
          {title}
        </div>
      )}
      <div className="bg-[#1e1e1e] text-white p-4 overflow-x-auto max-w-full">
        <pre className="text-sm font-mono whitespace-pre-wrap break-words overflow-hidden max-w-full">
          <code className={language ? `language-${language}` : ''}>
            {value}
          </code>
        </pre>
      </div>
    </div>
  )
}

export default CodeBlockRenderer 