import subprocess
import os
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any
import zipfile
import io

class NextJSProjectGenerator:
    """Next.js项目生成器"""
    
    def __init__(self):
        self.base_commands = [
            "npm", "node", "npx"
        ]
    
    def check_prerequisites(self) -> Dict[str, bool]:
        """检查必要的依赖是否安装"""
        results = {}
        for cmd in self.base_commands:
            try:
                subprocess.run([cmd, "--version"], capture_output=True, check=True)
                results[cmd] = True
            except (subprocess.CalledProcessError, FileNotFoundError):
                results[cmd] = False
        return results
    
    def create_project(self, project_name: str = "ai-nextjs-project", 
                      page_title: str = "欢迎来到宠物之家",
                      button_text: str = "了解更多") -> Dict[str, Any]:
        """创建Next.js项目"""
        
        # 检查依赖
        prereqs = self.check_prerequisites()
        if not all(prereqs.values()):
            missing = [cmd for cmd, available in prereqs.items() if not available]
            return {
                "success": False,
                "error": f"Missing prerequisites: {', '.join(missing)}",
                "logs": []
            }
        
        logs = []
        temp_dir = None
        
        try:
            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            project_path = os.path.join(temp_dir, project_name)
            
            logs.append(f"📁 创建项目目录: {project_path}")
            os.makedirs(project_path, exist_ok=True)
            os.chdir(project_path)
            
            # 1. 初始化 Next.js 项目
            logs.append("🚀 初始化 Next.js 项目...")
            result = subprocess.run([
                "npx", "create-next-app@latest", ".", 
                "--no-ts", "--no-eslint", "--no-tailwind", 
                "--app", "--import-alias", "@/*", "--yes"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                logs.append(f"❌ Next.js 初始化失败: {result.stderr}")
                return {"success": False, "error": "Next.js initialization failed", "logs": logs}
            
            logs.append("✅ Next.js 项目初始化成功")
            
            # 2. 初始化 shadcn
            logs.append("🎨 初始化 shadcn/ui...")
            result = subprocess.run([
                "npx", "shadcn@latest", "init", "-y", "--tailwind"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                logs.append(f"⚠️ shadcn 初始化警告: {result.stderr}")
            else:
                logs.append("✅ shadcn/ui 初始化成功")
            
            # 3. 安装 button 组件
            logs.append("📦 安装 shadcn Button 组件...")
            result = subprocess.run([
                "npx", "shadcn@latest", "add", "button", "-y"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode != 0:
                logs.append(f"⚠️ Button 组件安装警告: {result.stderr}")
            else:
                logs.append("✅ Button 组件安装成功")
            
            # 4. 写入自定义页面代码
            logs.append("📝 生成页面代码...")
            page_code = f'''import {{ Button }} from "@/components/ui/button";

export default function Home() {{
  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <h1 className="text-4xl font-bold">{page_title}</h1>
      <Button className="mt-6">{button_text}</Button>
    </main>
  );
}}
'''
            
            page_path = os.path.join(project_path, "app", "page.tsx")
            
            # 确保目录存在
            os.makedirs(os.path.dirname(page_path), exist_ok=True)
            
            with open(page_path, "w", encoding="utf-8") as f:
                f.write(page_code)
            
            logs.append("✅ 页面代码生成成功")
            
            # 5. 安装依赖
            logs.append("📦 安装项目依赖...")
            result = subprocess.run([
                "npm", "install"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                logs.append(f"❌ 依赖安装失败: {result.stderr}")
                return {"success": False, "error": "Dependencies installation failed", "logs": logs}
            
            logs.append("✅ 依赖安装成功")
            
            # 6. 构建项目
            logs.append("🔨 构建项目...")
            result = subprocess.run([
                "npm", "run", "build"
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode != 0:
                logs.append(f"❌ 项目构建失败: {result.stderr}")
                return {"success": False, "error": "Build failed", "logs": logs}
            
            logs.append("✅ 项目构建成功")
            
            # 7. 创建项目压缩包
            logs.append("📦 打包项目文件...")
            zip_buffer = io.BytesIO()
            
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for root, dirs, files in os.walk(project_path):
                    # 排除 node_modules 和 .next 目录中的文件
                    dirs[:] = [d for d in dirs if d not in ['node_modules', '.next']]
                    
                    for file in files:
                        file_path = os.path.join(root, file)
                        # 计算相对路径
                        arc_name = os.path.relpath(file_path, project_path)
                        zip_file.write(file_path, arc_name)
            
            zip_data = zip_buffer.getvalue()
            logs.append("✅ 项目打包完成")
            
            return {
                "success": True,
                "project_path": project_path,
                "logs": logs,
                "zip_data": zip_data,
                "project_name": project_name
            }
            
        except subprocess.TimeoutExpired:
            logs.append("❌ 操作超时")
            return {"success": False, "error": "Operation timed out", "logs": logs}
        except Exception as e:
            logs.append(f"❌ 执行出错: {str(e)}")
            return {"success": False, "error": str(e), "logs": logs}
        finally:
            # 清理临时目录
            if temp_dir and os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
