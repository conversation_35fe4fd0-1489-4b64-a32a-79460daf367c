'use client'

import React from 'react'

interface SVGRendererProps {
  content: string
}

const SVGRenderer: React.FC<SVGRendererProps> = ({ content }) => {
  console.log('SVG渲染 - 内容:', content) // 调试日志
  
  try {
    // 检查SVG内容是否包含svg标签
    const hasSvgTag = content.trim().startsWith('<svg')
    console.log('SVG渲染 - 是否有svg标签:', hasSvgTag) // 调试日志
    
    if (hasSvgTag) {
      // 如果已经有svg标签，直接渲染
      return (
        <div className="w-full h-full flex items-center justify-center p-4 bg-white overflow-auto">
          <div 
            className="max-w-full max-h-full border border-gray-200 rounded"
            style={{ 
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: '550px',
              minWidth: '550px'
            }}
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </div>
      )
    } else {
      // 如果没有svg标签，包装在svg标签中
      const wrappedSvg = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 400" width="400" height="400">
        ${content}
      </svg>`
      
      console.log('SVG渲染 - 包装后的内容:', wrappedSvg) // 调试日志
      
      return (
        <div className="w-full h-full flex items-center justify-center p-4 bg-white overflow-auto">
          <div 
            className="max-w-full max-h-full border border-gray-200 rounded"
            style={{ 
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: '300px',
              minWidth: '300px'
            }}
            dangerouslySetInnerHTML={{ __html: wrappedSvg }}
          />
        </div>
      )
    }
  } catch (error) {
    console.error('SVG渲染错误:', error)
    return (
      <div className="w-full h-full flex items-center justify-center p-4 bg-red-50">
        <div className="text-center text-red-600">
          <p className="mb-2">SVG渲染失败</p>
          <p className="text-sm">请检查SVG代码格式</p>
          <details className="mt-4 text-left text-xs">
            <summary className="cursor-pointer">显示原始内容</summary>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-black">{content}</pre>
          </details>
        </div>
      </div>
    )
  }
}

export default SVGRenderer 