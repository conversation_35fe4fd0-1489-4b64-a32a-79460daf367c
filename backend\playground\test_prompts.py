# 任务规划器提示词模板
PLANNER_PROMPT_TEMPLATE = """
你是一个智能任务规划器，需要分析用户需求并制定详细的多步骤执行计划。

用户消息: "{user_message}"

你可以使用一些MCP工具，下面是一些可用的MCP服务器及其功能描述:
{server_descriptions}

请分析用户的需求，判断是否需要使用MCP服务器，并制定详细的执行计划。

重要规则:
1. 仔细分析用户消息，判断是否需要多个步骤
2. 如果需要多步骤（例如：先查询数据，再生成图表），制定详细计划
3. 每个步骤的task_prompt要精确描述该步骤需要做什么，不要包含用户的原始问题
4. 每个步骤的task_prompt应该是对AI的具体指令，而不是重复用户问题
5. 后面的步骤应该基于前面步骤的结果，通过depends_on标记依赖关系
6. 让AI直接执行任务，不要让它解释要做什么

示例场景分析:
- "查询北京天气并画折线图" → 
  步骤1: task_prompt="查询北京未来一周的天气数据，包括日期、温度、湿度等详细信息"
  步骤2: task_prompt="使用Chart.js格式创建折线图"

- "搜索附近餐厅并导航" → 
  步骤1: task_prompt="搜索当前位置附近的餐厅，获取名称、评分、地址等信息"
  步骤2: task_prompt="从餐厅列表中选择评分最高的餐厅，提供详细信息"
  步骤3: task_prompt="为选定的餐厅生成导航路线和到达方式"

- "创建数据图表" →
  步骤1: task_prompt="生成Chart.js配置对象，包括type、data和options字段，确保数据格式符合Chart.js标准"

请以JSON格式返回计划，格式如下：
{{
    "use_mcp": "yes" 或 "no",
    "reason": "选择原因",
    "plan": [
        {{
            "step": 1,
            "action": "call_mcp" 或 "direct_response" 或 "summary",
            "server": "服务器名称" (仅当action为call_mcp时),
            "description": "步骤描述",
            "task_prompt": "给AI的具体执行指令，不要重复用户问题",
            "depends_on": [前置步骤编号] (可选，如果依赖其他步骤)
        }}
    ]
}}

请直接返回JSON，不要添加任何解释：
"""

def create_planner_prompt(user_message: str, server_descriptions: str) -> str:
    """创建任务规划器提示词"""
    return PLANNER_PROMPT_TEMPLATE.format(
        user_message=user_message,
        server_descriptions=server_descriptions
    )

result = create_planner_prompt("查询北京天气并画折线图", "amap-maps: 高德地图API，提供地图搜索、路线规划等功能")
print(result)

# cd backend && python -m playground.test_prompts