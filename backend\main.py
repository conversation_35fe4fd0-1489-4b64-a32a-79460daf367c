import json
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, Response
from agent import LLMChat
from agent.llm_service import ModelManager
from config.models import ChatRequest, ExecutePlanRequest, SandboxAnalyzeRequest, WebpageGenerateRequest
from sandbox.e2b import get_sandbox_service
from sandbox.webpage import NextJSProjectGenerator

model_manager = ModelManager()

app = FastAPI(title="MCP AutoGen Chat API")

ALLOWED_ORIGINS = [
    "https://zenus.agentspro.cn",     # 你的前端域名
    "https://zenusbe.agentspro.cn",   # 如果前端直接访问后端，也可加
    "http://localhost:3000",
    "http://localhost:8000",
    "http://************:3000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.post("/sandbox/analyze")
async def sandbox_analyze(
    file: UploadFile = File(...),
    analysis_request: str = Form(...),
    model: str = Form(default="anthropic-claude-3.5-sonnet")
):
    """Sandbox CSV 分析接口"""
    try:
        # 验证文件类型
        if not file.filename or not file.filename.endswith('.csv'):
            raise HTTPException(status_code=400, detail="只支持CSV文件")
        
        # 验证模型是否可用
        if not model_manager.get_model_config(model):
            raise HTTPException(status_code=400, detail=f"Model {model} is not available")
        
        # 读取文件内容
        file_content = await file.read()
        
        # 创建模型客户端
        model_client = model_manager.create_client(model)
        
        # 创建请求对象
        request_data = SandboxAnalyzeRequest(
            analysis_request=analysis_request,
            model=model,
            file_content=file_content
        )
        
        # 使用sandbox服务分析数据（AI生成代码在sandbox内部完成）
        sandbox_service = get_sandbox_service()
        result = await sandbox_service.analyze_csv_with_ai(request_data, model_client)
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")

@app.post("/chat/stream")
async def chat_endpoint(request: ChatRequest):
    """流式聊天接口"""
    if not request.message.strip():
        raise HTTPException(status_code=400, detail="Message cannot be empty")

    # 验证模型是否可用
    if not model_manager.get_model_config(request.model):
        raise HTTPException(status_code=400, detail=f"Model {request.model} is not available")

    # 处理 chat_history 默认值
    chat_history = request.chat_history or []

    return StreamingResponse(
        LLMChat.chat_stream(request.message, request.mcp_servers, request.model, chat_history),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

@app.post("/chat/execute-plan")
async def execute_plan_endpoint(request: ExecutePlanRequest):
    """执行用户确认的计划接口"""
    if not request.plan or not request.plan.plan:
        raise HTTPException(status_code=400, detail="Execution plan cannot be empty")

    # 验证模型是否可用
    if not model_manager.get_model_config(request.model):
        raise HTTPException(status_code=400, detail=f"Model {request.model} is not available")

    # 处理 chat_history 和 original_message 默认值
    chat_history = request.chat_history or []
    original_message = request.original_message or ""

    return StreamingResponse(
        LLMChat.execute_plan_stream(request.plan, request.mcp_servers, request.model, original_message, chat_history),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

@app.post("/chat/execute-plan-step-by-step")
async def execute_plan_step_by_step_endpoint(request: ExecutePlanRequest):
    """分步执行用户确认的计划接口 - 每一步单独调用Agent"""
    if not request.plan or not request.plan.plan:
        raise HTTPException(status_code=400, detail="Execution plan cannot be empty")

    # 验证模型是否可用
    if not model_manager.get_model_config(request.model):
        raise HTTPException(status_code=400, detail=f"Model {request.model} is not available")

    # 处理 chat_history 和 original_message 默认值
    chat_history = request.chat_history or []
    original_message = request.original_message or ""

    return StreamingResponse(
        LLMChat.execute_plan_step_by_step(request.plan, request.mcp_servers, request.model, original_message, chat_history),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "ok", "message": "API is running"}

@app.get("/mcp-servers")
async def get_mcp_servers():
    """获取可用的MCP服务器列表"""
    try:
        with open("config/mcp-stdio.json", "r", encoding="utf-8") as f:
            cfg = json.load(f)
        return {"servers": list(cfg["mcpServers"].keys())}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load MCP servers: {str(e)}")

@app.get("/models")
async def get_available_models():
    """获取可用的模型列表"""
    try:
        models = model_manager.get_available_models()
        return {"models": models}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to load models: {str(e)}")


@app.post("/webpage/generate")
async def generate_webpage(request: WebpageGenerateRequest):
    """生成Next.js网页项目"""
    try:
        generator = NextJSProjectGenerator()
        result = generator.create_project(
            project_name=request.project_name,
            page_title=request.page_title,
            button_text=request.button_text
        )
        
        if result["success"]:
            # 返回成功结果（不包含zip数据，太大了）
            return {
                "success": True,
                "logs": result["logs"],
                "project_name": result["project_name"]
            }
        else:
            return {
                "success": False,
                "error": result["error"],
                "logs": result["logs"]
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Project generation failed: {str(e)}")


@app.post("/webpage/download")
async def download_webpage_project(request: WebpageGenerateRequest):
    """生成并下载Next.js网页项目"""
    try:
        generator = NextJSProjectGenerator()
        result = generator.create_project(
            project_name=request.project_name,
            page_title=request.page_title,
            button_text=request.button_text
        )
        
        if result["success"]:
            # 返回zip文件
            return Response(
                content=result["zip_data"],
                media_type="application/zip",
                headers={
                    "Content-Disposition": f"attachment; filename={result['project_name']}.zip"
                }
            )
        else:
            raise HTTPException(status_code=500, detail=result["error"])
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Project generation failed: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) 