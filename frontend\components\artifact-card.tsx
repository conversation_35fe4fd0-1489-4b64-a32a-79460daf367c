'use client'

import React from 'react'
import { Code, Eye, FileText, Image } from 'lucide-react'

interface ArtifactCardProps {
  title?: string
  type: 'html' | 'javascript' | 'python' | 'react' | 'markdown' | 'text' | 'json' | 'css' | 'svg'
  isStreaming?: boolean
  onClick: () => void
}

export default function ArtifactCard({ title, type, isStreaming, onClick }: ArtifactCardProps) {
  const getTypeIcon = () => {
    switch (type) {
      case 'react':
        return <Code className="w-4 h-4 text-blue-500" />
      case 'html':
        return <FileText className="w-4 h-4 text-orange-500" />
      case 'svg':
        {/* eslint-disable-next-line jsx-a11y/alt-text */}
        return <Image className="w-4 h-4 text-purple-500" />
      case 'javascript':
        return <Code className="w-4 h-4 text-yellow-500" />
      default:
        return <FileText className="w-4 h-4 text-gray-500" />
    }
  }

  const getTypeLabel = () => {
    const labels: Record<string, string> = {
      'react': 'React 组件',
      'html': 'HTML 页面',
      'svg': 'SVG 图形',
      'javascript': 'JavaScript',
      'python': 'Python 脚本',
      'markdown': 'Markdown',
      'json': 'JSON 数据',
      'css': 'CSS 样式',
      'text': '文本文件'
    }
    return labels[type] || type
  }

  return (
    <div 
      onClick={onClick}
      className="flex items-center gap-3 px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg cursor-pointer hover:from-blue-100 hover:to-indigo-100 transition-all duration-200 shadow-sm hover:shadow-md w-full"
    >
      <div className="flex-shrink-0">
        {getTypeIcon()}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h4 className="text-sm font-semibold text-gray-900 truncate">
            {title || getTypeLabel()}
          </h4>
          {isStreaming && (
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
            </div>
          )}
        </div>
      </div>
      
      <div className="flex-shrink-0">
        <Eye className="w-4 h-4 text-gray-400" />
      </div>
    </div>
  )
} 