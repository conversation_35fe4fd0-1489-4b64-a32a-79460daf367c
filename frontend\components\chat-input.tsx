'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Plus } from 'lucide-react'
import Image from 'next/image'
import { McpModal } from './mcp-modal'
import { ModelConfigModal } from './model-config-modal'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface Model {
  id: string
  name: string
  provider: string
  display_name?: string
}

interface ModelConfig {
  id: string
  name: string
  display_name?: string
  provider: string
  apiKey: string
  baseUrl?: string
  endpoint?: string
}

interface ChatSettings {
  mcpServers: string[]
  model: string
}

interface ChatInputProps {
  onSendMessage: (message: string) => Promise<boolean | void>
  isLoading: boolean
  settings: ChatSettings
  onSettingsChange: (settings: ChatSettings) => void
}

export function ChatInput({ onSendMessage, isLoading, settings, onSettingsChange }: ChatInputProps) {
  const [message, setMessage] = useState('')
  const [isHovering, setIsHovering] = useState(false)
  const [showMcpModal, setShowMcpModal] = useState(false)
  const [showModelConfigModal, setShowModelConfigModal] = useState(false)
  const [availableModels, setAvailableModels] = useState<Model[]>([])
  const [customModels, setCustomModels] = useState<ModelConfig[]>([])
  const [isMobile, setIsMobile] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    const handleResize = () => {
      checkMobile()
    }

    checkMobile()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 获取可用模型列表
  useEffect(() => {
    const fetchModels = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/models`)
        if (response.ok) {
          const data = await response.json()
          console.log('Fetched models:', data.models) // 调试日志
          setAvailableModels(data.models || [])
        } else {
          console.error('Failed to fetch models')
          // 不设置任何默认模型，让用户自己选择
          setAvailableModels([])
        }
      } catch (error) {
        console.error('Error fetching models:', error)
        // 不设置任何默认模型，让用户自己选择
        setAvailableModels([])
      }
    }

    fetchModels()
  }, []) // 移除依赖项，避免无限循环

  // 自动调整 textarea 高度
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      const scrollHeight = textareaRef.current.scrollHeight
      const maxHeight = isMobile ? 120 : 150 // 移动端限制更小的高度
      textareaRef.current.style.height = Math.min(scrollHeight, maxHeight) + 'px'
    }
  }, [message, isMobile])

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (message.trim() && !isLoading) {
      const success = await onSendMessage(message.trim())
      // 只有在成功发送消息时才清空输入框
      if (success !== false) {
        setMessage('')
      }
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  // Removed unused mcpServers variable

  const handleModelChange = (value: string) => {
    if (value === 'add-custom') {
      setShowModelConfigModal(true)
    } else {
      onSettingsChange({ ...settings, model: value })
    }
  }

  // 添加自定义模型
  const handleAddCustomModel = (model: ModelConfig) => {
    setCustomModels(prev => [...prev, model])
  }


  // 获取提供商图标
  const getProviderIcon = (provider: string) => {
    const iconMap = {
      'openai': '/openai.png',
      'anthropic': '/anthropic.png',
      'google': '/google.png',
      'deepseek': '/deepseek.png'
    }
    
    return iconMap[provider as keyof typeof iconMap] || '/llm.png'
  }

  // 渲染提供商图标组件
  const renderProviderIcon = (provider: string, className = "w-4 h-4") => {
    const iconSrc = getProviderIcon(provider)
    return (
      <Image 
        src={iconSrc} 
        alt={provider} 
        width={16} 
        height={16} 
        className={className}
      />
    )
  }

  // 处理附件选择
  const handleAttachmentSelect = (type: string) => {
    // TODO: 处理不同类型的附件
    console.log('Selected attachment type:', type)
  }

  return (
    <>
      <div className={`bg-white z-50 ${
        isMobile ? '' : ''
      }`}>
        <div className="w-full">
          <div className={`${isMobile ? 'p-2' : 'p-3'} rounded-3xl w-full`} style={{ 
            backgroundColor: '#f2f3f5',
            border: '1px solid #dee2eb'
          }}>            
            {/* 输入表单 */}
            <form onSubmit={handleSubmit} className={`w-full ${isMobile ? 'mb-2' : 'mb-4'}`}>
              <textarea
                ref={textareaRef}
                value={message}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder="Ask anything"
                className={`w-full ${isMobile ? 'px-1 pb-2' : 'px-1 pb-4'} bg-transparent border-none focus:outline-none text-gray-700 ${
                  isMobile ? 'text-base' : 'text-sm'
                } placeholder-gray-400 resize-none overflow-hidden`}
                disabled={isLoading}
                rows={1}
                style={{ 
                  minHeight: isMobile ? '24px' : '20px',
                  maxHeight: isMobile ? '120px' : '150px'
                }}
              />
            </form>
            
            {/* 底部工具栏 - 重新布局 */}
            <div className="flex items-center justify-between">
              {/* 左侧功能按钮区域 */}
              <div className={`flex ${isMobile ? 'space-x-1' : 'space-x-1'} items-center`}>
                {/* 附件按钮 - 最左侧 */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <button 
                      type="button"
                      className={`flex items-center space-x-1 ${
                        isMobile ? 'px-2 py-2' : 'px-2 py-2'
                      } text-gray-600 hover:bg-gray-200 rounded-full transition-colors ${
                        isMobile ? 'text-xs' : 'text-xs'
                      } cursor-pointer ${
                        isMobile ? 'min-h-[36px] min-w-[44px]' : ''
                      }`}
                    >
                      <Image src="/attachment.svg" alt="Attachment" width={12} height={12} className={`${isMobile ? 'w-4 h-4' : 'w-4.5 h-4.5'}`} />
                    </button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent side="top" align="start" className="min-w-[200px] rounded-xl">
                    <DropdownMenuItem 
                      onClick={() => handleAttachmentSelect('photos')}
                      className="cursor-pointer"
                    >
                      <svg className="w-4 h-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                      </svg>
                      <span>添加照片和文件</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleAttachmentSelect('apps')}
                      className="cursor-pointer"
                    >
                      <Image src="/AddFromApp.svg" alt="AddFromApp" width={12} height={12} className={`${isMobile ? 'w-4 h-4' : 'w-4 h-4'}`} />
                      <span>从应用添加</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Tools配置按钮 */}
                <button 
                  type="button"
                  onClick={() => setShowMcpModal(true)}
                  className={`flex items-center space-x-1 ${
                    isMobile ? 'px-2 py-2' : 'px-2 py-2'
                  } text-gray-600 hover:bg-gray-200 rounded-full transition-colors ${
                    isMobile ? 'text-xs' : 'text-sm'
                  } cursor-pointer ${
                    isMobile ? 'min-h-[36px] min-w-[44px]' : ''
                  }`}
                >
                  <Image src="/tools.svg" alt="Tools" width={12} height={12} className={`${isMobile ? 'w-4 h-4' : 'w-4 h-4'}`} />
                  {!isMobile && <span>Tools</span>}
                </button>
              </div>

              {/* 右侧发送区域 */}
              <div className={`flex ${isMobile ? 'space-x-1' : 'space-x-2'} items-center`}>
                {/* 模型选择 */}
                <Select value={settings.model || ''} onValueChange={handleModelChange}>
                  <SelectTrigger className={`${
                    isMobile ? 
                      (settings.model ? 'w-auto min-w-[100px] pl-2 pr-3 py-2 h-[36px] cursor-pointer' : 'w-auto min-w-[80px] pl-2 pr-3 py-2 h-[36px] cursor-pointer') :
                      (settings.model ? 'w-auto min-w-[140px] max-w-[300px] pl-2 py-2 h-[32px] cursor-pointer' : 'w-auto min-w-[90px] pl-2 py-2 h-[32px] cursor-pointer')
                  } text-gray-600 text-sm bg-transparent hover:bg-gray-200 transition-colors border-none shadow-none focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:outline-none data-[state=open]:bg-transparent data-[state=open]:ring-0`}>
                    <div className="flex items-center space-x-2 w-full">
                      {(() => {
                        // 如果没有选择模型，显示默认状态
                        if (!settings.model) {
                          return isMobile ? (
                            <span className="text-sm text-gray-400">选择模型</span>
                          ) : (
                            <span className="text-sm text-gray-400">选择模型</span>
                          )
                        }

                        // 获取当前选中的模型
                        const customModel = customModels.find(model => model.id === settings.model)
                        const defaultModel = availableModels.find(model => model.id === settings.model)
                        const currentModel = customModel || defaultModel
                        
                        console.log('Model selection debug:', {
                          currentModelId: settings.model,
                          customModel,
                          defaultModel,
                          currentModel,
                          availableModels: availableModels.length,
                          isMobile
                        }) // 调试日志
                        
                        if (currentModel) {
                          return isMobile ? (
                            <div className="flex items-center space-x-1">
                              {renderProviderIcon(currentModel.provider, "w-4 h-4")}
                              <span className="text-sm truncate">{currentModel.display_name || currentModel.name}</span>
                            </div>
                          ) : (
                            <>
                              {renderProviderIcon(currentModel.provider, "w-4 h-4 flex-shrink-0")}
                              <span className="text-sm truncate flex-1 min-w-0">{currentModel.display_name || currentModel.name}</span>
                            </>
                          )
                        }
                        
                        // 如果选择的模型不存在，显示错误状态
                        console.log('Selected model not found:', settings.model) // 调试日志
                        return isMobile ? (
                            <span className="text-sm text-red-400">模型不存在</span>
                        ) : (
                          <>
                            <span className="text-sm text-red-400">模型不存在</span>
                          </>
                        )
                      })()}
                    </div>
                  </SelectTrigger>
                  <SelectContent className="bg-white border border-gray-200 shadow-lg">
                    {/* 默认模型 */}
                    {availableModels && availableModels.length > 0 ? (
                      availableModels.map((model) => (
                        <SelectItem 
                          key={model.id} 
                          value={model.id}
                          className="text-sm hover:bg-gray-100 cursor-pointer"
                        >
                          <div className="flex items-center gap-2">
                            {renderProviderIcon(model.provider)}
                            <span>{model.display_name || model.name}</span>
                          </div>
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem 
                        key="loading" 
                        value="loading"
                        disabled
                        className="text-sm text-gray-400"
                      >
                        加载中...
                      </SelectItem>
                    )}
                    
                    {/* 自定义模型分隔线 */}
                    {customModels.length > 0 && (
                      <>
                        <div key="separator" className="px-2 py-1">
                          <div className="border-t border-gray-200"></div>
                        </div>
                        <div key="custom-label" className="px-2 py-1 text-xs text-gray-500 font-medium">
                          自定义模型
                        </div>
                        {customModels.map((model) => (
                          <SelectItem 
                            key={`custom-${model.id}`} 
                            value={model.id}
                            className="text-sm hover:bg-gray-100"
                          >
                            <div className="flex items-center gap-2">
                              {renderProviderIcon(model.provider)}
                              <span>{model.display_name || model.name}</span>
                              <span className="text-xs text-gray-400">(自定义)</span>
                            </div>
                          </SelectItem>
                        ))}
                      </>
                    )}
                    
                    {/* 添加自定义模型选项 */}
                    <div key="add-separator" className="px-2 py-1">
                      <div className="border-t border-gray-200"></div>
                    </div>
                    <SelectItem key="add-custom" value="add-custom" className="text-sm hover:bg-blue-50 text-blue-600 cursor-pointer">
                      <div className="flex items-center gap-2">
                        <Plus className="w-4 h-4" />
                        <span>添加自定义模型</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                
                {/* 发送按钮 */}
                <button 
                  type="submit" 
                  onClick={handleSubmit}
                  onMouseEnter={() => setIsHovering(true)}
                  onMouseLeave={() => setIsHovering(false)}
                  className={`${
                    isMobile ? 'p-2' : 'p-2'
                  } rounded-full text-white transition-colors ${
                    !message.trim() 
                      ? 'cursor-not-allowed' 
                      : 'cursor-pointer'
                  } ${
                    isMobile ? 'min-h-[40px] min-w-[40px]' : 'min-h-[36px] min-w-[36px]'
                  }`}
                  style={{
                    backgroundColor: message.trim() 
                      ? (isHovering ? '#4d4d4d' : '#000000') 
                      : '#d7d7d7',
                  }}
                  disabled={!message.trim() || isLoading}
                >
                  <svg 
                    className={`${isMobile ? 'w-6 h-6' : 'w-5 h-5'}`} 
                    viewBox="0 0 20 20" 
                    fill="currentColor" 
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M8.99992 16V6.41407L5.70696 9.70704C5.31643 10.0976 4.68342 10.0976 4.29289 9.70704C3.90237 9.31652 3.90237 8.6835 4.29289 8.29298L9.29289 3.29298L9.36907 3.22462C9.76184 2.90427 10.3408 2.92686 10.707 3.29298L15.707 8.29298L15.7753 8.36915C16.0957 8.76192 16.0731 9.34092 15.707 9.70704C15.3408 10.0732 14.7618 10.0958 14.3691 9.7754L14.2929 9.70704L10.9999 6.41407V16C10.9999 16.5523 10.5522 17 9.99992 17C9.44764 17 8.99992 16.5523 8.99992 16Z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* MCP配置模态框 */}
      <McpModal
        isOpen={showMcpModal}
        onClose={() => setShowMcpModal(false)}
        settings={settings}
        onSettingsChange={onSettingsChange}
      />

      {/* 模型配置模态框 */}
      <ModelConfigModal
        isOpen={showModelConfigModal}
        onClose={() => setShowModelConfigModal(false)}
        settings={settings}
        onSettingsChange={onSettingsChange}
        onModelAdd={handleAddCustomModel}
      />
    </>
  )
} 