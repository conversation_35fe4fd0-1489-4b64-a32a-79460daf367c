import json
from autogen_ext.tools.mcp import StdioServerParams, StreamableHttpServerParams

def get_mcp_server_params(name: str):
    """获取MCP服务器参数"""
    # with open("config/mcp-stdio.json", "r", encoding="utf-8") as f:
    #     cfg = json.load(f)["mcpServers"][name]
    # return StdioServerParams(**cfg)
    with open("config/mcp-streamable-http.json", "r", encoding="utf-8") as f:
        cfg = json.load(f)["mcpServers"][name]
    return StreamableHttpServerParams(**cfg)