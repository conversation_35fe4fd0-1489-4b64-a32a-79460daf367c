'use client'

import React from 'react'
import { X } from 'lucide-react'
import Image from 'next/image'
import toast from 'react-hot-toast'

interface ModelConfig {
  id: string
  name: string
  provider: string
  apiKey: string
  baseUrl?: string
  endpoint?: string
}

interface ChatSettings {
  mcpServers: string[]
  model: string
}

interface ModelConfigModalProps {
  isOpen: boolean
  onClose: () => void
  settings: ChatSettings
  onSettingsChange: (settings: ChatSettings) => void
  onModelAdd: (model: ModelConfig) => void
}

export function ModelConfigModal({ isOpen, onClose, settings, onSettingsChange, onModelAdd }: ModelConfigModalProps) {
  // 表单状态
  const [formData, setFormData] = React.useState({
    name: '',
    provider: 'openai',
    apiKey: '',
    baseUrl: '',
    endpoint: ''
  })

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      provider: 'openai',
      apiKey: '',
      baseUrl: '',
      endpoint: ''
    })
  }

  if (!isOpen) return null

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleCancel = () => {
    resetForm()
    onClose()
  }

  const handleSubmit = () => {
    // 验证必填字段
    if (!formData.name.trim()) {
      toast.error('请输入模型名称')
      return
    }
    if (!formData.apiKey.trim()) {
      toast.error('请输入API密钥')
      return
    }

    // 生成模型ID
    const modelId = `custom-${formData.provider}-${Date.now()}`
    
    // 创建模型配置
    const newModel: ModelConfig = {
      id: modelId,
      name: formData.name,
      provider: formData.provider,
      apiKey: formData.apiKey,
      baseUrl: formData.baseUrl || undefined,
      endpoint: formData.endpoint || undefined
    }

    // 添加模型
    onModelAdd(newModel)
    
    // 自动选择新添加的模型
    onSettingsChange({ ...settings, model: modelId })
    
    toast.success('自定义模型添加成功！')
    resetForm()
    onClose()
  }

  const providerOptions = [
    { value: 'openai', label: 'OpenAI', icon: '/openai.png' },
    { value: 'azure', label: 'Azure OpenAI', icon: '/azure.png' },
    { value: 'anthropic', label: 'Anthropic', icon: '/anthropic.png' },
    { value: 'ollama', label: 'Ollama', icon: '/ollama.png' },
    { value: 'custom', label: 'Custom', icon: '/llm.png' }
  ]

  // 获取当前选中提供商的图标
  const getCurrentProviderIcon = () => {
    const currentProvider = providerOptions.find(p => p.value === formData.provider)
    return currentProvider?.icon || '/llm.png'
  }

  return (
    <div className="fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white/95 backdrop-blur-lg rounded-xl shadow-2xl border border-white/20 max-w-lg w-full max-h-[85vh] flex flex-col">
        {/* 头部 - 固定在顶部 */}
        <div className="flex-shrink-0 bg-white/95 backdrop-blur-xl border-b border-gray-200/50 px-5 py-3 rounded-t-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              {/* 第一行：图标和标题 */}
              <div className="flex items-center gap-2 mb-1">
                <div className="flex items-center gap-1">
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  {formData.provider && (
                    <Image 
                      src={getCurrentProviderIcon()} 
                      alt={formData.provider} 
                      width={20} 
                      height={20} 
                      className="w-5 h-5"
                    />
                  )}
                </div>
                <h2 className="text-lg font-semibold text-gray-900">添加自定义模型</h2>
              </div>
              {/* 第二行：描述 */}
              <p className="text-xs text-gray-600">
                配置您自己的AI模型接入信息
              </p>
            </div>
            <button
              onClick={handleCancel}
              className="p-1.5 hover:bg-gray-100/50 rounded-full transition-colors cursor-pointer"
            >
              <X className="w-4 h-4 text-gray-500" />
            </button>
          </div>
        </div>

        {/* 内容区域 - 可滚动 */}
        <div className="flex-1 overflow-y-auto p-5">
          <div className="space-y-4">
            {/* 模型名称 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                模型名称 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="例如：Claude 3.5 Sonnet"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
              />
            </div>

            {/* 提供商选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                提供商 <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.provider}
                onChange={(e) => handleInputChange('provider', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
              >
                {providerOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* API密钥 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                API密钥 <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                value={formData.apiKey}
                onChange={(e) => handleInputChange('apiKey', e.target.value)}
                placeholder="sk-..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
              />
            </div>

            {/* 基础URL (OpenAI兼容) */}
            {(formData.provider === 'openai' || formData.provider === 'ollama' || formData.provider === 'custom') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  基础URL
                </label>
                <input
                  type="url"
                  value={formData.baseUrl}
                  onChange={(e) => handleInputChange('baseUrl', e.target.value)}
                  placeholder={formData.provider === 'ollama' ? 'http://localhost:11434' : 'https://api.openai.com/v1'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
                />
              </div>
            )}

            {/* Azure端点 */}
            {formData.provider === 'azure' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Azure端点
                </label>
                <input
                  type="url"
                  value={formData.endpoint}
                  onChange={(e) => handleInputChange('endpoint', e.target.value)}
                  placeholder="https://your-resource.openai.azure.com/"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
                />
              </div>
            )}

            {/* Anthropic主机 */}
            {formData.provider === 'anthropic' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  API主机
                </label>
                <input
                  type="url"
                  value={formData.baseUrl}
                  onChange={(e) => handleInputChange('baseUrl', e.target.value)}
                  placeholder="https://api.anthropic.com"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors"
                />
              </div>
            )}

            {/* 帮助信息 */}
            <div className="bg-blue-50/80 backdrop-blur-sm border border-blue-200/50 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <svg className="w-4 h-4 text-blue-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="text-xs text-blue-700">
                  <p className="font-medium">配置说明</p>
                  <ul className="mt-1 space-y-1">
                    <li>• API密钥将安全存储在本地</li>
                    <li>• 不同提供商需要不同的配置参数</li>
                    <li>• 自定义模型将显示在模型列表中</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部按钮 - 固定在底部 */}
        <div className="flex-shrink-0 bg-white/95 backdrop-blur-xl border-t border-gray-200/50 px-5 py-3 rounded-b-xl shadow-sm">
          <div className="flex justify-end gap-2">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-sm text-gray-600 hover:bg-gray-100/50 rounded-lg transition-colors cursor-pointer"
            >
              取消
            </button>
            <button
              onClick={handleSubmit}
              className="px-4 py-2 text-sm bg-blue-600/90 backdrop-blur-sm text-white hover:bg-blue-700/90 rounded-lg transition-colors cursor-pointer"
            >
              添加模型
            </button>
          </div>
        </div>
      </div>
    </div>
  )
} 