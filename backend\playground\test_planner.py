import asyncio
from agent import <PERSON>Manager
from autogen_core.models import UserMessage

async def main(model_name):
    model_manager = ModelManager()
    model_client = model_manager.create_client(model_name)    
    result = await model_client.create([UserMessage(content="What is the capital of France?", source="user")])  # type: ignore
    print(result)
    

if __name__ == "__main__":
    asyncio.run(main("deepseek-r1-0528"))

# cd backend && python -m playground.test_planner
