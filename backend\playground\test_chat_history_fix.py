#!/usr/bin/env python3
"""
测试聊天历史修复功能
"""
import asyncio
from config.models import ChatMessage
from agent.llm_chat import LLMChat

def test_build_task_with_context():
    """测试构建包含聊天历史的任务提示"""
    
    # 测试空历史
    result = LLMChat._build_task_with_context("你好", [])
    print("空历史测试:")
    print(f"输入: '你好'")
    print(f"输出: '{result}'")
    print()
    
    # 测试字典格式的聊天历史
    dict_history = [
        {"role": "user", "content": "你好"},
        {"role": "assistant", "content": "你好！有什么可以帮助你的吗？"},
        {"role": "user", "content": "请介绍一下Python"}
    ]
    
    result = LLMChat._build_task_with_context("现在告诉我JavaScript", dict_history)
    print("字典格式历史测试:")
    print(f"输入: '现在告诉我JavaScript'")
    print(f"输出:\n{result}")
    print()
    
    # 测试 Pydantic 模型格式的聊天历史
    pydantic_history = [
        ChatMessage(role="user", content="你好"),
        ChatMessage(role="assistant", content="你好！有什么可以帮助你的吗？"),
        ChatMessage(role="user", content="请介绍一下Python")
    ]
    
    result = LLMChat._build_task_with_context("现在告诉我JavaScript", pydantic_history)
    print("Pydantic模型格式历史测试:")
    print(f"输入: '现在告诉我JavaScript'")
    print(f"输出:\n{result}")
    print()

if __name__ == "__main__":
    test_build_task_with_context()
