import json
from typing import List, Dict, Union, Optional
from agent.llm_service import ModelManager
from autogen_core.models import UserMessage


class TaskEvaluator:
    """智能任务完成评估器"""
    
    def __init__(self, model_name: str):
        model_manager = ModelManager()
        self.model_client = model_manager.create_client(model_name)
        self.evaluation_prompt_template = self._get_evaluation_prompt_template()
    
    def _get_evaluation_prompt_template(self) -> str:
        return """
你是一个智能任务完成评估器，需要判断一个多步骤任务是否已经真正完成。

原始用户需求: "{original_request}"

执行计划概述:
{plan_summary}

已完成的步骤及其结果:
{step_results}

请评估:
1. 用户的原始需求是否已经得到满足？
2. 所有关键目标是否已经达成？
3. 是否还需要额外的步骤来完善结果？
4. 执行结果的质量如何？

请以JSON格式返回评估结果:
{{
    "is_complete": true/false,
    "completion_score": 0-100,
    "missing_elements": ["缺失的要素1", "缺失的要素2"],
    "quality_issues": ["质量问题1", "质量问题2"],
    "suggested_next_steps": [
        {{
            "action": "call_mcp" 或 "direct_response" 或 "summary",
            "description": "建议的下一步描述",
            "reason": "为什么需要这一步"
        }}
    ],
    "summary": "简要总结当前完成状况"
}}

如果任务已经完成且质量良好，is_complete应该为true，suggested_next_steps应该为空数组。
"""
    
    async def evaluate_task_completion(
        self, 
        original_request: str, 
        execution_plan: Dict, 
        step_results: Dict[int, Dict]
    ) -> Dict[str, Union[bool, int, List, str]]:
        """评估任务是否完成"""
        
        # 构建计划概述
        plan_summary = self._format_plan_summary(execution_plan)
        
        # 构建步骤结果摘要
        results_summary = self._format_step_results(step_results)
        
        # 构建评估提示词
        prompt = self.evaluation_prompt_template.format(
            original_request=original_request,
            plan_summary=plan_summary,
            step_results=results_summary
        )
        
        try:
            response = await self.model_client.create([UserMessage(content=prompt, source="user")])
            result_text = response.content
            
            # 解析JSON结果
            try:
                # 清理可能的代码块标记
                cleaned_text = result_text.strip()
                if cleaned_text.startswith('```json'):
                    cleaned_text = cleaned_text[7:]
                if cleaned_text.endswith('```'):
                    cleaned_text = cleaned_text[:-3]
                cleaned_text = cleaned_text.strip()
                
                evaluation = json.loads(cleaned_text)
                
                # 验证结果格式
                if "is_complete" not in evaluation:
                    evaluation["is_complete"] = False
                if "completion_score" not in evaluation:
                    evaluation["completion_score"] = 50
                if "missing_elements" not in evaluation:
                    evaluation["missing_elements"] = []
                if "suggested_next_steps" not in evaluation:
                    evaluation["suggested_next_steps"] = []
                
                return evaluation
                
            except json.JSONDecodeError:
                print(f"⚠️ 任务评估JSON解析失败: {result_text}")
                return self._get_default_evaluation()
                
        except Exception as e:
            print(f"❌ 任务评估失败: {e}")
            return self._get_default_evaluation()
    
    def _format_plan_summary(self, execution_plan: Dict) -> str:
        """格式化执行计划概述"""
        plan_steps = execution_plan.get("plan", [])
        summary_lines = []
        
        for step in plan_steps:
            step_num = step.get("step", "?")
            action = step.get("action", "unknown")
            description = step.get("description", "无描述")
            summary_lines.append(f"步骤{step_num}: {action} - {description}")
        
        return "\n".join(summary_lines) if summary_lines else "无执行计划"
    
    def _format_step_results(self, step_results: Dict[int, Dict]) -> str:
        """格式化步骤结果"""
        if not step_results:
            return "暂无步骤结果"
        
        result_lines = []
        for step_num, result in step_results.items():
            action = result.get("action", "unknown")
            content = result.get("content", "")
            # 限制内容长度，避免提示词过长
            content_preview = content[:200] + "..." if len(content) > 200 else content
            result_lines.append(f"步骤{step_num}({action}): {content_preview}")
        
        return "\n".join(result_lines)
    
    def _get_default_evaluation(self) -> Dict:
        """获取默认评估结果"""
        return {
            "is_complete": False,
            "completion_score": 50,
            "missing_elements": ["评估失败，无法确定"],
            "quality_issues": ["无法进行质量评估"],
            "suggested_next_steps": [],
            "summary": "任务评估系统出现问题，建议人工检查"
        }


class AdaptivePlanner:
    """自适应规划器 - 根据执行结果动态调整计划"""
    
    def __init__(self, model_name: str):
        model_manager = ModelManager()
        self.model_client = model_manager.create_client(model_name)
        self.evaluator = TaskEvaluator(model_name)
    
    async def should_continue_execution(
        self, 
        original_request: str, 
        execution_plan: Dict, 
        step_results: Dict[int, Dict]
    ) -> Dict[str, Union[bool, List, str]]:
        """判断是否应该继续执行，以及需要什么额外步骤"""
        
        # 使用评估器评估任务完成情况
        evaluation = await self.evaluator.evaluate_task_completion(
            original_request, execution_plan, step_results
        )
        
        # 如果任务已经完成且质量良好，不需要继续
        if evaluation.get("is_complete", False) and evaluation.get("completion_score", 0) >= 80:
            return {
                "should_continue": False,
                "additional_steps": [],
                "reason": "任务已完成且质量良好",
                "evaluation": evaluation
            }
        
        # 如果有建议的后续步骤，则继续执行
        suggested_steps = evaluation.get("suggested_next_steps", [])
        if suggested_steps:
            return {
                "should_continue": True,
                "additional_steps": suggested_steps,
                "reason": f"需要{len(suggested_steps)}个额外步骤来完善结果",
                "evaluation": evaluation
            }
        
        # 如果完成度低但没有建议步骤，标记为需要人工介入
        if evaluation.get("completion_score", 0) < 70:
            return {
                "should_continue": False,
                "additional_steps": [],
                "reason": "任务完成度较低，可能需要人工介入或重新规划",
                "evaluation": evaluation
            }
        
        # 默认情况：任务基本完成
        return {
            "should_continue": False,
            "additional_steps": [],
            "reason": "任务基本完成",
            "evaluation": evaluation
        } 