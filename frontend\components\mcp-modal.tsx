'use client'

import React from 'react'
import { X } from 'lucide-react'
import Image from 'next/image'
import { Switch } from '@/components/ui/switch'
import toast from 'react-hot-toast'

interface ChatSettings {
  mcpServers: string[]  // 改为数组支持多选
  model: string
}

interface McpModalProps {
  isOpen: boolean
  onClose: () => void
  settings: ChatSettings
  onSettingsChange: (settings: ChatSettings) => void
}

export function McpModal({ isOpen, onClose, settings, onSettingsChange }: McpModalProps) {
  // 添加临时状态，只有点击确认才保存
  const [tempMcpServers, setTempMcpServers] = React.useState<string[]>(settings.mcpServers || [])
  
  // 当modal打开时，同步当前设置到临时状态
  React.useEffect(() => {
    if (isOpen) {
      setTempMcpServers(settings.mcpServers || [])
    }
  }, [isOpen, settings.mcpServers])
  
  if (!isOpen) return null

  const mcpServers = [
    { 
      value: 'amap-maps', 
      label: 'amap-maps',
      description: '高德地图 MCP 服务器，提供地图相关功能',
      tools: [
        'maps_regeocode', 'maps_geo', 'maps_ip_location', 'maps_weather', 
        'maps_search_detail', 'maps_bicycling', 'maps_direction_walking', 
        'maps_direction_driving', 'maps_direction_transit_integrated', 
        'maps_distance', 'maps_text_search', 'maps_around_search'
      ]
    },
    { 
      value: 'github', 
      label: 'github',
      description: 'GitHub MCP 服务器，提供代码仓库管理功能',
      tools: ['github_search_repositories', 'github_get_file', 'github_list_files', 'github_create_issue']
    },
    { 
      value: 'airbnb', 
      label: 'airbnb',
      description: 'Airbnb MCP 服务器，提供房源搜索和详情查询',
      tools: ['airbnb_search', 'airbnb_listing_details']
    },
    { 
      value: 'playwright', 
      label: 'playwright',
      description: 'Playwright MCP 服务器，提供浏览器自动化功能',
      tools: [
        'browser_close', 'browser_resize', 'browser_console_messages', 
        'browser_handle_dialog', 'browser_file_upload', 'browser_install', 
        'browser_press_key', 'browser_navigate', 'browser_navigate_back', 
        'browser_navigate_forward', 'browser_network_requests', 'browser_pdf_save', 
        'browser_take_screenshot', 'browser_snapshot', 'browser_click', 
        'browser_drag', 'browser_hover', 'browser_type', 'browser_select_option', 
        'browser_tab_list', 'browser_tab_new', 'browser_tab_select', 
        'browser_tab_close', 'browser_generate_playwright_test', 'browser_wait_for'
      ]
    },
    { 
      value: 'fetch', 
      label: 'fetch',
      description: 'Fetch MCP 服务器，提供网络请求功能',
      tools: [
        'fetch'
      ]
    },
    { 
      value: '302ai-sandbox-mcp', 
      label: '302ai-sandbox-mcp',
      description: 'OpenAI MCP 服务器，提供OpenAI API功能',
      tools: [
        'writeSandboxFiles', 'directRunCode', 'listSandboxFiles', 'downloadSandboxFiles', 'runCommand', 'listSandboxes', "killSandbox", "createSandbox"
      ]
    },
    { 
      value: 'quickchart-server', 
      label: 'quickchart-server',
      description: 'QuickChart MCP 服务器，提供图表生成功能',
      tools: ['generate_chart', 'download_chart']
    },
    { 
      value: 'howtocook-mcp', 
      label: 'howtocook-mcp',
      description: 'HowToCook MCP 服务器，提供菜谱查询功能',
      tools: ['mcp_howtocook_getAllRecipes', 'mcp_howtocook_getRecipesByCategory', 'mcp_howtocook_recommendMeals', 'mcp_howtocook_whatToEat']
    },
    {
      value: 'exa',
      label: 'exa',
      description: '',
      tools: ['web_search_exa', 'research_paper_search_exa', 'company_search_exa', 'crawling_exa', 
              'competitor_finder_exa', 'linkedin_search_exa', 'wikipedia_search_exa', 'github_search_exa'
            ]
    }
  ]

  // 计算当前选中服务器的总工具数（使用临时状态）
  const calculateTotalTools = (selectedServers: string[]) => {
    return selectedServers.reduce((total, serverValue) => {
      const server = mcpServers.find(s => s.value === serverValue)
      return total + (server?.tools.length || 0)
    }, 0)
  }

  const currentToolCount = calculateTotalTools(tempMcpServers)
  const isToolCountExceeded = currentToolCount > 40

  const handleMcpServerToggle = (value: string) => {
    let newServers: string[]
    
    if (tempMcpServers.includes(value)) {
      // 如果已选中，则移除
      newServers = tempMcpServers.filter(server => server !== value)
    } else {
      // 如果未选中，则添加
      newServers = [...tempMcpServers, value]
      
      // 检查新的工具总数
      const newToolCount = calculateTotalTools(newServers)
      if (newToolCount > 40) {
        toast.error(`工具数量将达到 ${newToolCount} 个，超过推荐的 40 个限制。这可能会影响性能。`, {
          duration: 4000,
          icon: '⚠️'
        })
      }
    }
    
    setTempMcpServers(newServers)
  }

  // 取消操作
  const handleCancel = () => {
    setTempMcpServers(settings.mcpServers || [])
    onClose()
  }

  // 确认保存
  const handleConfirm = () => {
    onSettingsChange({ ...settings, mcpServers: tempMcpServers })
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white/95 backdrop-blur-lg rounded-xl shadow-2xl border border-white/20 max-w-5xl w-full max-h-[95vh] flex flex-col">
        {/* 头部 - 固定在顶部 */}
        <div className="flex-shrink-0 bg-white/95 backdrop-blur-xl border-b border-gray-200/50 px-5 py-5 rounded-t-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              {/* 第一行：图标和标题 */}
              <div className="flex items-center gap-2 mb-1">
              <Image src="/tools.svg" alt="Tools" width={24} height={24} />
                <h2 className="text-2xl font-semibold text-gray-900">Tools</h2>
              </div>
              {/* 第二行：描述 */}
              <p className="text-xs text-gray-600">
                Tool is a way to offer Agent new capabilities.
              </p>
            </div>
            <button
              onClick={handleCancel}
              className="p-1.5 hover:bg-gray-100/50 rounded-full transition-colors cursor-pointer"
            >
              <X className="w-4 h-4 text-gray-500" />
            </button>
          </div>
        </div>

        {/* 内容区域 - 可滚动 */}
        <div className="flex-1 overflow-y-auto p-5">
          <div className="space-y-3">
            {mcpServers.map((server) => {
              const isSelected = tempMcpServers.includes(server.value)
              
              return (
                <div
                  key={server.value}
                  className="bg-white/95 backdrop-blur-sm rounded-lg p-3 border border-gray-200/60 shadow-lg"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${isSelected ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                      <h3 className="text-gray-900 font-medium text-sm">{server.label}</h3>
                    </div>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={isSelected}
                        onCheckedChange={() => handleMcpServerToggle(server.value)}
                        className="data-[state=checked]:bg-green-500 cursor-pointer"
                      />
                    </div>
                  </div>

                  <div className="mb-2">
                    <div className="flex items-center gap-2 mb-1.5">
                      <svg className="w-3 h-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <span className="text-gray-600 text-xs font-medium">Tools:</span>
                    </div>
                    <div className="flex flex-wrap gap-1.5">
                      {server.tools.map((tool) => (
                        <span
                          key={tool}
                          className="px-1.5 py-0.5 bg-gray-100/80 backdrop-blur-sm text-gray-700 text-xs rounded border border-gray-300/50"
                        >
                          {tool}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* 底部按钮 - 固定在底部 */}
        <div className="flex-shrink-0 bg-white/95 backdrop-blur-xl border-t border-gray-200/50 px-5 py-3 rounded-b-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <span>已选择 {tempMcpServers.length} 个服务器</span>
              {tempMcpServers.length > 0 && (
                <>
                  <span className="mx-1">•</span>
                  <div className="flex items-center gap-1">
                  <span className={`${isToolCountExceeded ? 'text-red-600 font-medium' : 'text-gray-700'}`}>
                    {currentToolCount} 个工具
                  </span>
                  {isToolCountExceeded && (
                      <div className="relative group">
                        <svg 
                          className="w-3 h-3 text-red-500 cursor-help" 
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                        >
                          <path 
                            strokeLinecap="round" 
                            strokeLinejoin="round" 
                            strokeWidth={2} 
                            d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
                          />
                        </svg>
                        
                        {/* Tooltip */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                          <div className="text-center">
                            <div className="font-medium">工具数量超过推荐限制</div>
                            <div className="mt-1">过多的工具可能会影响性能，某些模型可能不支持超过 40 个工具</div>
                          </div>
                          {/* 箭头 */}
                          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
                        </div>
                      </div>
                  )}
                  </div>
                </>
              )}
            </div>
            <div className="flex gap-2">
              <button
                onClick={handleCancel}
                className="px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-100/50 rounded-lg transition-colors cursor-pointer"
              >
                取消
              </button>
              <button
                onClick={handleConfirm}
                className="px-3 py-1.5 text-sm bg-black backdrop-blur-sm text-white hover:bg-[#4d4d4d] rounded-lg transition-colors cursor-pointer"
              >
                确认
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 