'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, Use<PERSON>, <PERSON>, <PERSON><PERSON> } from 'lucide-react'
import toast from 'react-hot-toast'
import ToolCallDisplay from './tool-call-display'
import MarkdownRenderer from '../lib/Renderer/MarkdownRenderer'
import { ChatInput } from './chat-input'
import ArtifactsDisplay from './artifacts-display'
import ArtifactCard from './artifact-card'
import ExecutionPlanDisplay from './execution-plan-display'
import { parseArtifacts, type ArtifactData, hasStartedArtifacts, extractStreamingReactComponentsContent, extractStreamingHtmlComponentsContent, detectStreamingArtifactsRealtime } from '@/lib/Parser/ArtifactsParser'

interface ToolCall {
  id: string
  toolName: string
  parameters?: Record<string, unknown>
  result?: Record<string, unknown> | string | number | boolean | null
  status: 'calling' | 'success' | 'error'
  error?: string
  timestamp?: number  // 添加时间戳用于排序
  insertPosition?: number  // 在消息内容中的插入位置
}

// 添加混合内容项接口
interface MessageItem {
  type: 'text' | 'tool_call'
  content?: string
  toolCall?: ToolCall
  timestamp: number
}

interface ExecutionPlan {
  use_mcp: 'yes' | 'no'
  reason: string
  plan: Array<{
    step: number
    action: 'call_mcp' | 'direct_response' | 'summary'
    server?: string
    description: string
    status?: 'pending' | 'executing' | 'completed' | 'skipped'
  }>
}

interface Message {
  id: string
  content: string
  role: 'user' | 'assistant' | 'system'
  timestamp: number
  type?: 'message' | 'tool_call' | 'error'
  toolCalls?: ToolCall[]
  artifacts?: ArtifactData[]
  streamingArtifacts?: ArtifactData[]  // 流式检测到的artifacts
  executionPlan?: ExecutionPlan
  messageItems?: MessageItem[]  // 新增：混合内容项
}

interface ChatSettings {
  mcpServers: string[]
  model: string
}

interface ChatInterfaceProps {
  sidebarCollapsed?: boolean
}

export default function ChatInterface({ }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [hasStartedStreaming, setHasStartedStreaming] = useState(false)
  const [settings, setSettings] = useState<ChatSettings>({
    mcpServers: [],
    model: ''
  })
  const [currentArtifacts, setCurrentArtifacts] = useState<ArtifactData[]>([])
  const [showArtifacts, setShowArtifacts] = useState(false)
  const [streamingContent, setStreamingContent] = useState('')
  const [isStreamingArtifacts, setIsStreamingArtifacts] = useState(false)
  const [currentStreamingMessageId, setCurrentStreamingMessageId] = useState<string | null>(null)
  const [hasSessionPlan, setHasSessionPlan] = useState<boolean>(false) // 跟踪当前 session 是否已有任务卡片
  const [isMobile, setIsMobile] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const [userHasScrolled, setUserHasScrolled] = useState(false) // 用户是否手动滚动过
  const [isNearBottom, setIsNearBottom] = useState(true) // 是否接近底部
  const lastScrollTop = useRef(0) // 记录上次滚动位置
  // Removed unused pendingExecutionPlan state

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // 检查是否接近底部
  const checkIfNearBottom = (scrollElement: Element) => {
    const threshold = 100 // 100px的阈值
    const isNear = scrollElement.scrollHeight - scrollElement.scrollTop - scrollElement.clientHeight < threshold
    setIsNearBottom(isNear)
    return isNear
  }

  // 智能滚动到底部
  const scrollToBottom = useCallback((force = false) => {
    if (scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollElement) {
        // 只有在用户没有手动滚动过，或者强制滚动，或者用户已经在底部附近时才自动滚动
        if (force || (!userHasScrolled && isNearBottom)) {
          scrollElement.scrollTop = scrollElement.scrollHeight
          setIsNearBottom(true)
        }
      }
    }
  }, [userHasScrolled, isNearBottom])

  // 监听滚动事件
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]')
      if (scrollElement) {
        const handleScroll = () => {
          const currentScrollTop = scrollElement.scrollTop
          const isUserScrolling = Math.abs(currentScrollTop - lastScrollTop.current) > 5 // 5px的阈值避免微小抖动
          
          // 检查是否接近底部
          checkIfNearBottom(scrollElement)
          
          // 如果用户向上滚动了明显的距离，标记为手动滚动
          if (isUserScrolling && currentScrollTop < lastScrollTop.current) {
            setUserHasScrolled(true)
          }
          
          // 如果用户滚动到底部附近，重置手动滚动标记
          if (scrollElement.scrollHeight - currentScrollTop - scrollElement.clientHeight < 10) {
            setUserHasScrolled(false)
          }
          
          lastScrollTop.current = currentScrollTop
        }

        scrollElement.addEventListener('scroll', handleScroll)
        return () => scrollElement.removeEventListener('scroll', handleScroll)
      }
    }
  }, [])

  // 新消息时的智能滚动
  useEffect(() => {
    // 在流式输出期间频繁检查滚动
    if (isLoading || hasStartedStreaming) {
      const interval = setInterval(() => {
        scrollToBottom()
      }, 100) // 每100ms检查一次
      
      return () => clearInterval(interval)
    } else {
      // 消息完成时滚动一次
      scrollToBottom()
    }
  }, [messages, isLoading, hasStartedStreaming, userHasScrolled, isNearBottom, scrollToBottom])

  // 重置滚动状态（发送新消息时）
  const resetScrollState = () => {
    setUserHasScrolled(false)
    setIsNearBottom(true)
    scrollToBottom(true) // 强制滚动到底部
  }

  // Removed unused resetSessionState function

  // Removed unused clearChat function

  // 处理 artifacts 显示
  const handleShowArtifacts = (artifacts: ArtifactData[], streaming = false) => {
    setCurrentArtifacts(artifacts)
    setShowArtifacts(true)
    if (!streaming) {
      setIsStreamingArtifacts(false)
      setStreamingContent('')
    }
  }

  const handleCloseArtifacts = () => {
    setShowArtifacts(false)
    setCurrentArtifacts([])
    setStreamingContent('')
    setIsStreamingArtifacts(false)
    setCurrentStreamingMessageId(null)
  }

  const handleStartStreamingForMessage = (messageId: string) => {
    setIsStreamingArtifacts(true)
    setStreamingContent('')
    setCurrentStreamingMessageId(messageId)
    handleShowArtifacts([], true) // 展开空面板开始流式输出
  }

  const copyMessage = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      toast.success('消息已复制到剪贴板')
    } catch {
      toast.error('复制失败')
    }
  }

  // 处理执行计划变更
  const handlePlanChange = (messageId: string, updatedPlan: ExecutionPlan) => {
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, executionPlan: updatedPlan }
        : msg
    ))
  }

  // 处理确认执行
  const handleConfirmExecution = async (messageId: string, plan: ExecutionPlan) => {
    if (isLoading) {
      toast.error('请等待当前任务完成')
      return
    }

    // 检查是否选择了模型
    if (!settings.model) {
      toast.error('请先选择一个模型')
      return
    }

    // 获取原始用户消息
    const originalMessage = messages.find(msg => msg.id === messageId)
    const originalUserMessage = originalMessage?.content || plan.reason

    // 重置滚动状态
    resetScrollState()

    // 设置加载状态
    setIsLoading(true)
    setHasStartedStreaming(false)

    // 用于fallback的消息ID，但通常不会用到，因为每个步骤都会创建自己的消息
    const executionMessageId = (Date.now() + Math.random()).toString()

    // 构建聊天历史（包含当前所有消息）
    const chatHistory = messages.map(msg => ({
      role: msg.role,
      content: msg.content,
      timestamp: msg.timestamp
    }))

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/chat/execute-plan-step-by-step`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan: plan,
          mcp_servers: settings.mcpServers,
          model: settings.model,
          original_message: originalUserMessage,
          chat_history: chatHistory
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      const decoder = new TextDecoder()

      // 执行消息已经在上面创建了，这里不需要再更新

      // 处理流式响应（类似于sendMessage中的逻辑）
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))

              if (!hasStartedStreaming) {
                setHasStartedStreaming(true)
                setIsLoading(false)
              }

              if (data.type === 'end') {
                break
              } else if (data.type === 'error') {
                toast.error(data.content)
                break
              } else if (data.type === 'new_message') {
                // 创建新的步骤消息气泡
                const stepMessage: Message = {
                  id: data.message_id,
                  content: '',
                  role: 'assistant',
                  timestamp: Date.now(),
                  toolCalls: [],
                  artifacts: []
                }
                setMessages(prev => [...prev, stepMessage])
                console.log(`📝 创建步骤 ${data.step} 的新消息气泡: ${data.description}`)
              } else if (data.type === 'step_start') {
                // 步骤开始 - 可以在这里添加日志或其他处理
                console.log(`🔄 开始执行步骤 ${data.step}: ${data.action}`)
              } else if (data.type === 'step_complete') {
                // 步骤完成 - 可以在这里添加日志或其他处理
                console.log(`✅ 完成步骤 ${data.step}`)
              } else if (data.type === 'tool_call_start') {
                // 开始新的工具调用 - 检查是否已存在相同ID的工具调用
                console.log('🔧 [分步执行] 收到工具调用开始事件:', data) // 添加调试日志
                const targetMessageId = data.message_id || executionMessageId
                const toolCallId = data.tool_call_id || Date.now().toString()
                const currentTimestamp = Date.now()

                // 找到目标消息并获取其当前状态
                const targetMessage = messages.find(msg => msg.id === targetMessageId)
                const existingToolCall = targetMessage?.toolCalls?.find(tc => tc.id === toolCallId)

                if (existingToolCall) {
                  // 更新现有的工具调用状态
                  setMessages(prev => prev.map(msg =>
                    msg.id === targetMessageId
                      ? {
                          ...msg,
                          toolCalls: msg.toolCalls?.map(tc =>
                            tc.id === toolCallId
                              ? {
                                  ...tc,
                                  toolName: data.tool_name || tc.toolName,
                                  parameters: data.parameters || tc.parameters,
                                  status: 'calling' as const,
                                  timestamp: currentTimestamp
                                }
                              : tc
                          ) || []
                        }
                      : msg
                  ))
                } else {
                  // 添加新的工具调用，并将其添加到混合内容项中
                  const newToolCall: ToolCall = {
                    id: toolCallId,
                    toolName: data.tool_name || 'unknown',
                    parameters: data.parameters,
                    status: 'calling',
                    timestamp: currentTimestamp
                  }

                  // 创建工具调用的消息项
                  const toolCallItem: MessageItem = {
                    type: 'tool_call',
                    toolCall: newToolCall,
                    timestamp: currentTimestamp
                  }

                  setMessages(prev => prev.map(msg =>
                    msg.id === targetMessageId
                      ? {
                          ...msg,
                          toolCalls: [...(msg.toolCalls || []), newToolCall],
                          messageItems: [...(msg.messageItems || []), toolCallItem]
                        }
                      : msg
                  ))
                }
              } else if (data.type === 'tool_call_result') {
                // 工具调用结果
                console.log('🔧 [分步执行] 收到工具调用结果事件:', data) // 添加调试日志
                const targetMessageId = data.message_id || executionMessageId
                const toolCallId = data.tool_call_id
                const result = data.result

                setMessages(prev => prev.map(msg =>
                  msg.id === targetMessageId
                    ? {
                        ...msg,
                        toolCalls: msg.toolCalls?.map(tc =>
                          tc.id === toolCallId ? { ...tc, result: result, status: 'success' as const } : tc
                        ) || [],
                        messageItems: msg.messageItems?.map(item =>
                          item.type === 'tool_call' && item.toolCall && item.toolCall.id === toolCallId
                            ? {
                                ...item,
                                toolCall: {
                                  ...item.toolCall,
                                  result: result,
                                  status: 'success' as const
                                }
                              }
                            : item
                        ) || []
                      }
                    : msg
                ))
              } else if (data.type === 'message_delta' && data.content) {
                // 处理执行结果的流式内容
                const targetMessageId = data.message_id || executionMessageId

                // 更新消息内容
                setMessages(prev => prev.map(msg => {
                  if (msg.id === targetMessageId) {
                    const updatedContent = (msg.content || '') + data.content

                    // 对当前消息进行实时artifacts检测
                    const realtimeResult = detectStreamingArtifactsRealtime(updatedContent)

                    // 添加调试日志
                    if (updatedContent.includes('<!DOCTYPE html') || updatedContent.includes('```html')) {
                      console.log('🔍 [分步执行] 检测到HTML内容:', {
                        messageId: targetMessageId,
                        hasArtifacts: realtimeResult.hasArtifacts,
                        shouldExpand: realtimeResult.shouldExpand,
                        artifactsCount: realtimeResult.artifacts.length,
                        isStreaming: realtimeResult.isStreaming,
                        contentPreview: updatedContent.substring(0, 200) + '...'
                      })
                    }

                    if (realtimeResult.hasArtifacts && realtimeResult.shouldExpand) {
                      // 立即展开artifacts面板
                      if (!showArtifacts) {
                        handleShowArtifacts(realtimeResult.artifacts, true)
                      } else {
                        // 如果面板已经展开，更新当前artifacts
                        setCurrentArtifacts(realtimeResult.artifacts)
                      }

                      return {
                        ...msg,
                        content: realtimeResult.remainingContent,
                        streamingArtifacts: realtimeResult.artifacts
                      }
                    }

                    return { ...msg, content: updatedContent }
                  }
                  return msg
                }))
              }
            } catch (e) {
              console.error('Failed to parse SSE data:', e)
            }
          }
        }
      }
    } catch (error) {
      console.error('Error executing plan:', error)
      toast.error('执行计划失败，请检查后端服务')
    } finally {
      setIsLoading(false)
      setHasStartedStreaming(false)
    }
  }

  const sendMessage = async (messageText: string): Promise<boolean | void> => {
    if (!messageText.trim() || isLoading) return false

    // 检查是否选择了模型
    if (!settings.model) {
      toast.error('请先选择一个模型')
      return false
    }

    // 重置滚动状态，确保新对话开始时自动滚动
    resetScrollState()

    const userMessage: Message = {
      id: Date.now().toString(),
      content: messageText.trim(),
      role: 'user',
      timestamp: Date.now()
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)
    setHasStartedStreaming(false)

    try {
      // 构建聊天历史（排除当前用户消息）
      const chatHistory = messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp
      }))

      const response = await fetch(`${process.env.NEXT_PUBLIC_BACKEND_URL}/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          mcp_servers: settings.mcpServers,
          model: settings.model,
          chat_history: chatHistory
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('Failed to get response reader')
      }

      // 创建一个AI助手消息
      const assistantMessageId = (Date.now() + 1).toString()
      let assistantMessage: Message = {
        id: assistantMessageId,
        content: '',
        role: 'assistant',
        timestamp: Date.now(),
        toolCalls: [],
        artifacts: []
      }

      // 立即添加到消息列表中
      setMessages(prev => [...prev, assistantMessage])

      const decoder = new TextDecoder()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              
              // 一旦开始接收数据，就隐藏加载状态
              if (!hasStartedStreaming) {
                setHasStartedStreaming(true)
                setIsLoading(false)
              }
              
              if (data.type === 'end') {
                break
              } else if (data.type === 'error') {
                toast.error(data.content)
                break
              } else if (data.type === 'execution_plan') {
                // 处理执行计划 - 只在 session 中第一次时创建任务卡片
                if (!hasSessionPlan) {
                  const planWithPendingStatus = {
                    ...data.plan,
                    plan: data.plan.plan.map((step: ExecutionPlan['plan'][0]) => ({ ...step, status: 'pending' }))
                  }

                  setMessages(prev => prev.map(msg =>
                    msg.id === assistantMessageId
                      ? {
                          ...msg,
                          executionPlan: planWithPendingStatus
                        }
                      : msg
                  ))

                  // 更新本地引用
                  assistantMessage = {
                    ...assistantMessage,
                    executionPlan: planWithPendingStatus
                  }

                  // 标记 session 已有任务卡片
                  setHasSessionPlan(true)
                } else {
                  // 后续对话不显示任务卡片，直接继续处理
                  console.log('🔄 Session 已有任务卡片，跳过显示执行计划')
                }
              } else if (data.type === 'waiting_confirmation') {
                // 处理等待确认消息
                const confirmationMessage = data.message || "请确认执行计划"

                setMessages(prev => prev.map(msg =>
                  msg.id === assistantMessageId
                    ? {
                        ...msg,
                        content: (msg.content || '') + '\n\n' + confirmationMessage
                      }
                    : msg
                ))

                // 更新本地引用
                assistantMessage.content += '\n\n' + confirmationMessage
              } else if (data.type === 'step_start') {
                // 处理步骤开始事件
                const stepNumber = data.step
                console.log(`🔄 开始执行步骤 ${stepNumber}: ${data.description}`)
                
                // 更新执行计划中相应步骤的状态为"执行中"
                setMessages(prev => prev.map(msg => 
                  msg.id === assistantMessageId 
                    ? { 
                        ...msg, 
                        executionPlan: msg.executionPlan ? {
                          ...msg.executionPlan,
                          plan: msg.executionPlan.plan.map(step => 
                            step.step === stepNumber 
                              ? { ...step, status: 'executing' as const }
                              : step
                          )
                        } : undefined
                      }
                    : msg
                ))
                
                // 更新本地引用
                if (assistantMessage.executionPlan) {
                  assistantMessage.executionPlan.plan = assistantMessage.executionPlan.plan.map(step => 
                    step.step === stepNumber 
                      ? { ...step, status: 'executing' as const }
                      : step
                  )
                }
              } else if (data.type === 'step_complete') {
                // 处理步骤完成事件
                const stepNumber = data.step
                console.log(`✅ 完成步骤 ${stepNumber}`)
                
                // 更新执行计划中相应步骤的状态为"已完成"
                setMessages(prev => prev.map(msg => 
                  msg.id === assistantMessageId 
                    ? { 
                        ...msg, 
                        executionPlan: msg.executionPlan ? {
                          ...msg.executionPlan,
                          plan: msg.executionPlan.plan.map(step => 
                            step.step === stepNumber 
                              ? { ...step, status: 'completed' as const }
                              : step
                          )
                        } : undefined
                      }
                    : msg
                ))
                
                // 更新本地引用
                if (assistantMessage.executionPlan) {
                  assistantMessage.executionPlan.plan = assistantMessage.executionPlan.plan.map(step => 
                    step.step === stepNumber 
                      ? { ...step, status: 'completed' as const }
                      : step
                  )
                }
              } else if (data.type === 'tool_call_start') {
                // 开始新的工具调用 - 检查是否已存在相同ID的工具调用
                console.log('🔧 收到工具调用开始事件:', data) // 添加调试日志
                const toolCallId = data.tool_call_id || Date.now().toString()
                const currentTimestamp = Date.now()
                const existingToolCall = assistantMessage.toolCalls?.find(tc => tc.id === toolCallId)
                
                if (existingToolCall) {
                  // 更新现有的工具调用状态
                  setMessages(prev => prev.map(msg => 
                    msg.id === assistantMessageId 
                      ? { 
                          ...msg, 
                          toolCalls: msg.toolCalls?.map(tc => 
                            tc.id === toolCallId 
                              ? {
                                  ...tc,
                                  toolName: data.tool_name || tc.toolName,
                                  parameters: data.parameters || tc.parameters,
                                  status: 'calling' as const,
                                  timestamp: currentTimestamp
                                }
                              : tc
                          ) || []
                        }
                      : msg
                  ))
                  
                  // 更新本地引用
                  assistantMessage = {
                    ...assistantMessage,
                    toolCalls: assistantMessage.toolCalls?.map(tc => 
                      tc.id === toolCallId 
                        ? { ...tc, toolName: data.tool_name || tc.toolName, parameters: data.parameters || tc.parameters, status: 'calling' as const, timestamp: currentTimestamp }
                        : tc
                    ) || []
                  }
                } else {
                  // 添加新的工具调用，并将其添加到混合内容项中
                  const newToolCall: ToolCall = {
                    id: toolCallId,
                    toolName: data.tool_name || 'unknown',
                    parameters: data.parameters,
                    status: 'calling',
                    timestamp: currentTimestamp
                  }
                  
                  // 创建工具调用的消息项
                  const toolCallItem: MessageItem = {
                    type: 'tool_call',
                    toolCall: newToolCall,
                    timestamp: currentTimestamp
                  }
                  
                  setMessages(prev => prev.map(msg => 
                    msg.id === assistantMessageId 
                      ? { 
                          ...msg, 
                          toolCalls: [...(msg.toolCalls || []), newToolCall],
                          messageItems: [...(msg.messageItems || []), toolCallItem]
                        }
                      : msg
                  ))
                  
                  // 更新assistantMessage的引用以便后续查找
                  assistantMessage = {
                    ...assistantMessage,
                    toolCalls: [...(assistantMessage.toolCalls || []), newToolCall],
                    messageItems: [...(assistantMessage.messageItems || []), toolCallItem]
                  }
                }
              } else if (data.type === 'tool_call_result') {
                // 工具调用结果
                console.log('🔧 收到工具调用结果事件:', data) // 添加调试日志
                const toolCallId = data.tool_call_id
                const result = data.result
                
                setMessages(prev => prev.map(msg => 
                  msg.id === assistantMessageId 
                    ? { 
                        ...msg, 
                        toolCalls: msg.toolCalls?.map(tc => 
                          tc.id === toolCallId ? { ...tc, result: result, status: 'success' as const } : tc
                        ) || [],
                        messageItems: msg.messageItems?.map(item => 
                          item.type === 'tool_call' && item.toolCall && item.toolCall.id === toolCallId 
                            ? { 
                                ...item, 
                                toolCall: { 
                                  ...item.toolCall, 
                                  result: result, 
                                  status: 'success' as const 
                                }
                              }
                            : item
                        ) || []
                      }
                    : msg
                ))
                
                // 更新assistantMessage的引用
                assistantMessage = {
                  ...assistantMessage,
                  toolCalls: assistantMessage.toolCalls?.map(tc => 
                    tc.id === toolCallId ? { ...tc, result: result, status: 'success' as const } : tc
                  ) || [],
                  messageItems: assistantMessage.messageItems?.map(item => 
                    item.type === 'tool_call' && item.toolCall && item.toolCall.id === toolCallId 
                      ? { 
                          ...item, 
                          toolCall: { 
                            ...item.toolCall, 
                            result: result, 
                            status: 'success' as const 
                          }
                        }
                      : item
                  ) || []
                }
              } else if (data.type === 'message' && data.content) {
                // 处理完整消息内容并解析 artifacts
                const parsed = parseArtifacts(data.content)
                
                setMessages(prev => prev.map(msg => 
                  msg.id === assistantMessageId 
                    ? { 
                        ...msg, 
                        content: parsed.content,
                        artifacts: parsed.artifacts,
                        streamingArtifacts: [] // 清除流式artifacts
                      }
                    : msg
                ))
                
                // 更新本地引用
                assistantMessage = {
                  ...assistantMessage,
                  content: parsed.content,
                  artifacts: parsed.artifacts,
                  streamingArtifacts: []
                }
                
                // 如果有 artifacts，显示
                if (parsed.artifacts.length > 0) {
                  handleShowArtifacts(parsed.artifacts)
                }
                
                // 停止流式输出
                setIsStreamingArtifacts(false)
                setStreamingContent('')
                setCurrentStreamingMessageId(null)
              } else if (data.type === 'message_delta' && data.content) {
                // 处理流式增量内容
                const deltaContent = data.content
                
                // 如果消息在混合内容模式下，更新混合内容项
                if (assistantMessage.messageItems && assistantMessage.messageItems.length > 0) {
                  // 查找最后一个文本项或创建新的文本项
                  const lastItem = assistantMessage.messageItems[assistantMessage.messageItems.length - 1]
                  
                  if (lastItem && lastItem.type === 'text') {
                    // 更新最后一个文本项
                    setMessages(prev => prev.map(msg => 
                      msg.id === assistantMessageId 
                        ? { 
                            ...msg, 
                            messageItems: msg.messageItems?.map((item, index) => 
                              index === (msg.messageItems?.length || 0) - 1 && item.type === 'text'
                                ? { ...item, content: (item.content || '') + deltaContent }
                                : item
                            ) || []
                          }
                        : msg
                    ))
                    
                    // 更新本地引用
                    if (assistantMessage.messageItems.length > 0) {
                      const lastIndex = assistantMessage.messageItems.length - 1
                      assistantMessage.messageItems[lastIndex] = {
                        ...assistantMessage.messageItems[lastIndex],
                        content: (assistantMessage.messageItems[lastIndex].content || '') + deltaContent
                      }
                    }
                  } else {
                    // 创建新的文本项
                    const newTextItem: MessageItem = {
                      type: 'text',
                      content: deltaContent,
                      timestamp: Date.now()
                    }
                    
                    setMessages(prev => prev.map(msg => 
                      msg.id === assistantMessageId 
                        ? { 
                            ...msg, 
                            messageItems: [...(msg.messageItems || []), newTextItem]
                          }
                        : msg
                    ))
                    
                    // 更新本地引用
                    assistantMessage.messageItems = [...assistantMessage.messageItems, newTextItem]
                  }
                } else {
                  // 传统模式：更新content字段
                  setMessages(prev => prev.map(msg => 
                    msg.id === assistantMessageId 
                      ? { ...msg, content: (msg.content || '') + deltaContent }
                      : msg
                  ))
                  
                  assistantMessage.content += deltaContent
                }

                // 实时检测artifacts
                const fullContent = assistantMessage.content || (
                  assistantMessage.messageItems 
                    ? assistantMessage.messageItems
                        .filter(item => item.type === 'text')
                        .map(item => item.content || '')
                        .join('')
                    : ''
                )
                
                // 使用新的实时artifacts检测
                const realtimeResult = detectStreamingArtifactsRealtime(fullContent)
                
                // 添加调试日志
                if (fullContent.includes('<!DOCTYPE html') || fullContent.includes('```html')) {
                  console.log('🔍 检测到HTML内容:', {
                    hasArtifacts: realtimeResult.hasArtifacts,
                    shouldExpand: realtimeResult.shouldExpand,
                    artifactsCount: realtimeResult.artifacts.length,
                    isStreaming: realtimeResult.isStreaming,
                    contentPreview: fullContent.substring(0, 200) + '...'
                  })
                }
                
                if (realtimeResult.hasArtifacts && realtimeResult.shouldExpand) {
                  // 更新消息的streamingArtifacts
                  setMessages(prev => prev.map(msg => 
                    msg.id === assistantMessageId 
                      ? { 
                          ...msg, 
                          streamingArtifacts: realtimeResult.artifacts,
                          // 更新显示的内容（移除代码块，显示占位符）
                          content: assistantMessage.messageItems ? msg.content : realtimeResult.remainingContent
                        }
                      : msg
                  ))
                  
                  // 更新本地引用
                  assistantMessage.streamingArtifacts = realtimeResult.artifacts
                  if (!assistantMessage.messageItems) {
                    assistantMessage.content = realtimeResult.remainingContent
                  }
                  
                  // 设置流式内容用于artifacts面板显示
                  if (realtimeResult.artifacts.length > 0) {
                    setStreamingContent(realtimeResult.artifacts[0].content)
                  }
                  
                  // 立即展开artifacts面板
                  if (!showArtifacts) {
                    handleShowArtifacts(realtimeResult.artifacts, true)
                  } else {
                    // 如果面板已经展开，更新当前artifacts
                    setCurrentArtifacts(realtimeResult.artifacts)
                  }
                }

                // 旧的检测逻辑保留作为备用
                if (!realtimeResult.hasArtifacts && hasStartedArtifacts(fullContent)) {
                  if (!isStreamingArtifacts) {
                    handleStartStreamingForMessage(assistantMessageId)
                  }
                  // 检查是否是 html-components
                  if (fullContent.includes('<html-components')) {
                    setStreamingContent(extractStreamingHtmlComponentsContent(fullContent))
                  } else if (fullContent.includes('<react-components')) {
                    setStreamingContent(extractStreamingReactComponentsContent(fullContent))
                  } else if (fullContent.includes('```html')) {
                    // 处理 ```html 代码块
                    const htmlMatch = fullContent.match(/```html\s*\n([\s\S]*?)(?:\n```|$)/i)
                    if (htmlMatch) {
                      setStreamingContent(htmlMatch[1])
                    }
                  } else if (fullContent.includes('```svg')) {
                    // 处理 ```svg 代码块
                    const svgMatch = fullContent.match(/```svg\s*\n([\s\S]*?)(?:\n```|$)/i)
                    if (svgMatch) {
                      setStreamingContent(svgMatch[1])
                    }
                  } else {
                    setStreamingContent(extractStreamingReactComponentsContent(fullContent))
                  }
                }
              }
            } catch (e) {
              console.error('Failed to parse SSE data:', e)
            }
          }
        }
      }
    } catch (error) {
      console.error('Error sending message:', error)
      toast.error('发送消息失败，请检查后端服务是否运行')
      return false
    } finally {
      setIsLoading(false)
      setHasStartedStreaming(false)
      setIsStreamingArtifacts(false)
      setCurrentStreamingMessageId(null)
    }

    return true // 成功发送消息
  }

  return (
    <div className="flex h-full bg-white">
      {/* 主聊天区域 */}
      <div className={`flex flex-col transition-all duration-300 ${
        showArtifacts 
          ? isMobile 
            ? 'hidden' // 移动端隐藏聊天区域，全屏显示 artifacts
            : 'w-1/2 bg-white' 
          : 'w-full bg-white'
      }`}>
        {/* 当未展开artifacts时，添加最大宽度和居中布局 */}
        <div className={`flex flex-col h-full ${
          !showArtifacts ? 'max-w-4xl mx-auto w-full bg-white' : 'w-full'
      }`}>
          {/* 消息区域 - 独立滚动 */}
          <div className="flex-1 overflow-hidden relative">
            <ScrollArea className={`h-full ${
              isMobile ? 'p-3' : showArtifacts ? 'p-6' : 'p-6 px-8'
            } ${!showArtifacts ? 'scroll-bar-right' : ''}`} ref={scrollAreaRef}>
              <div className={`space-y-4 pb-4 container-overflow-safe ${
                isMobile ? 'max-w-none' : 'max-w-full'
          }`}>
            {messages.length === 0 && (
                  <div className="flex items-center justify-center h-full min-h-[50vh]">
                    <div className="text-center">
                      <p className={`text-black font-bold ${
                        isMobile ? 'text-lg' : 'text-3xl'
                      }`}>How can I help you today?</p>
                    </div>
              </div>
            )}
            
            {messages
              .filter(message => 
                // 只显示有内容、有工具调用、有artifacts或有执行计划的消息
                message.content || (message.toolCalls && message.toolCalls.length > 0) || (message.artifacts && message.artifacts.length > 0) || message.executionPlan
              )
              // 去重，确保没有重复的消息ID
              .filter((message, index, arr) => 
                arr.findIndex(m => m.id === message.id) === index
              )
              .map((message) => {
                return (
              <div
                key={message.id}
                className={`flex ${
                  isMobile ? 'gap-2' : 'gap-3'
                } ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {message.role !== 'user' && (
                  <div className={`${
                    isMobile ? 'w-7 h-7' : 'w-8 h-8'
                  } rounded-full bg-[#f4f4f4] flex items-center justify-center flex-shrink-0`}>
                    <Bot className={`${
                      isMobile ? 'w-3.5 h-3.5' : 'w-4 h-4'
                    } text-blue-600`} />
                  </div>
                )}
                
                <div className={`${
                  message.role === 'user' 
                    ? isMobile 
                      ? 'max-w-[calc(100%-3rem)] min-w-0' // 为用户消息预留头像空间 (28px + gap)
                      : 'max-w-[calc(100%-3.5rem)] min-w-0' // 为用户消息预留头像空间 (32px + gap)
                    : 'flex-1 min-w-0' // 添加 min-w-0 防止 flex 子元素 overflow
                } space-y-2`}>
                  {/* 执行计划展示 - 在消息开头显示 */}
                  {message.executionPlan && (
                    <div className={`w-full ${
                          isMobile ? 'max-w-none' : 'max-w-full'
                    }`}>
                      <ExecutionPlanDisplay
                        plan={message.executionPlan}
                        className="mb-3"
                        isEditable={message.role === 'assistant' && !isLoading && !hasStartedStreaming}
                        onPlanChange={(updatedPlan) => handlePlanChange(message.id, updatedPlan)}
                        onConfirmExecution={(plan) => handleConfirmExecution(message.id, plan)}
                      />
                    </div>
                  )}

                  {/* 如果有混合内容项，按时间顺序渲染 */}
                  {message.messageItems && message.messageItems.length > 0 ? (
                    <div className="space-y-2">
                      {message.messageItems
                        .sort((a, b) => a.timestamp - b.timestamp)
                        .reduce((acc, item, index, items) => {
                          if (item.type === 'tool_call' && item.toolCall) {
                            // 渲染工具调用卡片
                            acc.push(
                              <div key={`tool-${item.toolCall.id}`} className={`w-full ${
                                    isMobile ? 'max-w-none' : 'max-w-full'
                              }`}>
                                <ToolCallDisplay
                                  toolName={item.toolCall.toolName}
                                  parameters={item.toolCall.parameters}
                                  result={item.toolCall.result}
                                  status={item.toolCall.status}
                                  error={item.toolCall.error}
                                />
                              </div>
                            )
                          } else if (item.type === 'text' && item.content) {
                            // 检查是否是连续文本的开始（前一个不是文本或这是第一个）
                            const prevItem = index > 0 ? items[index - 1] : null
                            const isTextStart = !prevItem || prevItem.type !== 'text'
                            
                            // 只有连续文本的开始才渲染文本气泡
                            if (isTextStart) {
                              // 收集连续的文本内容
                              let combinedText = item.content || ''
                              let nextIndex = index + 1
                              while (nextIndex < items.length && items[nextIndex].type === 'text') {
                                combinedText += items[nextIndex].content || ''
                                nextIndex++
                              }
                              
                              acc.push(
                                <div key={`text-group-${index}`} className="flex flex-col group">
                                  <div
                                    className={`relative rounded-lg ${
                                          isMobile ? 'px-3 py-2' : 'px-4 py-3'
                                    } ${
                                      message.role === 'user'
                                        ? `bg-blue-600 text-white max-w-full min-w-0 overflow-hidden`
                                        : 'bg-white max-w-full min-w-0 overflow-hidden'
                                    }`}
                                  >
                                    {message.role === 'user' ? (
                                      <p className={`whitespace-pre-wrap text-overflow-safe ${
                                        isMobile ? 'text-sm' : 'text-base'
                                      }`}>{combinedText}</p>
                                    ) : (
                                          <>
                                      <MarkdownRenderer content={combinedText} />
                                            
                                            {/* 在最后一个文本组后显示artifacts卡片 */}
                                            {nextIndex >= items.length && 
                                              ((message.artifacts && message.artifacts.length > 0) || 
                                               (message.streamingArtifacts && message.streamingArtifacts.length > 0)) && (
                                              <div className="mt-3 space-y-2">
                                                {/* 显示流式artifacts（如果存在） */}
                                                {message.streamingArtifacts && message.streamingArtifacts.length > 0 && 
                                                  message.streamingArtifacts.map((artifact) => (
                                                    <ArtifactCard
                                                      key={`streaming-${artifact.id}`}
                                                      title={artifact.title}
                                                      type={artifact.type}
                                                      isStreaming={true}
                                                      onClick={() => {
                                                        // 为流式artifacts创建临时数组并显示
                                                        handleShowArtifacts(message.streamingArtifacts || [])
                                                      }}
                                                    />
                                                  ))
                                                }
                                                
                                                {/* 显示最终的artifacts（如果没有流式artifacts） */}
                                                {(!message.streamingArtifacts || message.streamingArtifacts.length === 0) &&
                                                  message.artifacts && message.artifacts.length > 0 && 
                                                  message.artifacts.map((artifact) => (
                                                    <ArtifactCard
                                                      key={artifact.id}
                                                      title={artifact.title}
                                                      type={artifact.type}
                                                      isStreaming={isStreamingArtifacts && message.id === currentStreamingMessageId}
                                                      onClick={() => handleShowArtifacts(message.artifacts || [])}
                                                    />
                                                  ))
                                                }
                                              </div>
                                            )}
                                          </>
                                    )}
                                  </div>
                                </div>
                              )
                            }
                          }
                          return acc
                        }, [] as React.ReactNode[])}
                      
                      {/* 复制按钮 - 在最后显示 */}
                      {message.content && (
                        <div className={`mt-1 ${message.role === 'user' ? 'ml-auto' : ''}`}>
                          <button
                            onClick={() => copyMessage(message.content)}
                            className={`${
                              isMobile ? 'p-2.5' : 'p-2'
                            } rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200 cursor-pointer ${
                              isMobile ? 'min-h-[44px] min-w-[44px]' : ''
                            }`}
                            title="复制消息"
                          >
                            <Copy className={`${
                              isMobile ? 'w-5 h-5' : 'w-4 h-4'
                            }`} />
                          </button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <>
                      {/* 传统渲染：工具调用 + 消息内容 */}
                      {message.toolCalls && message.toolCalls.length > 0 && (
                        <div className={`space-y-2 w-full ${
                              isMobile ? 'max-w-none' : 'max-w-full'
                        }`}>
                          {message.toolCalls.map((toolCall) => (
                            <ToolCallDisplay
                              key={toolCall.id}
                              toolName={toolCall.toolName}
                              parameters={toolCall.parameters}
                              result={toolCall.result}
                              status={toolCall.status}
                              error={toolCall.error}
                            />
                          ))}
                        </div>
                      )}

                      {/* 消息内容 - 限制宽度的容器 */}
                      {message.content && (
                        <div className="flex flex-col group">
                          <div
                            className={`relative rounded-lg ${
                              isMobile ? 'px-3 py-2' : 'px-4 py-3'
                            } ${
                              message.role === 'user'
                                ? `bg-[#f4f4f4] text-black max-w-full min-w-0 overflow-hidden`
                                : 'bg-white max-w-full min-w-0 overflow-hidden'
                            }`}
                          >
                            {message.role === 'user' ? (
                              <p className={`whitespace-pre-wrap text-overflow-safe ${
                                isMobile ? 'text-sm' : 'text-base'
                              }`}>{message.content}</p>
                            ) : (
                                  <>
                              <MarkdownRenderer content={message.content} />
                                    
                                    {/* Artifact 卡片显示在消息气泡内部 */}
                                    {((message.artifacts && message.artifacts.length > 0) || 
                                      (message.streamingArtifacts && message.streamingArtifacts.length > 0)) && (
                                      <div className="mt-3 space-y-2">
                                        {/* 显示流式artifacts（如果存在） */}
                                        {message.streamingArtifacts && message.streamingArtifacts.length > 0 && 
                                          message.streamingArtifacts.map((artifact) => (
                                            <ArtifactCard
                                              key={`streaming-${artifact.id}`}
                                              title={artifact.title}
                                              type={artifact.type}
                                              isStreaming={true}
                                              onClick={() => {
                                                // 为流式artifacts创建临时数组并显示
                                                handleShowArtifacts(message.streamingArtifacts || [])
                                              }}
                                            />
                                          ))
                                        }
                                        
                                        {/* 显示最终的artifacts（如果没有流式artifacts） */}
                                        {(!message.streamingArtifacts || message.streamingArtifacts.length === 0) &&
                                          message.artifacts && message.artifacts.length > 0 && 
                                          message.artifacts.map((artifact) => (
                                            <ArtifactCard
                                              key={artifact.id}
                                              title={artifact.title}
                                              type={artifact.type}
                                              isStreaming={isStreamingArtifacts && message.id === currentStreamingMessageId}
                                              onClick={() => handleShowArtifacts(message.artifacts || [])}
                                            />
                                          ))
                                        }
                                      </div>
                                    )}
                                  </>
                            )}
                          </div>
                          
                          {/* 复制按钮 - 在消息气泡下方 */}
                          <div className={`mt-1 ${message.role === 'user' ? 'ml-auto' : ''}`}>
                            <button
                              onClick={() => copyMessage(message.content)}
                              className={`${
                                isMobile ? 'p-2.5' : 'p-2'
                              } rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors duration-200 cursor-pointer ${
                                isMobile ? 'min-h-[44px] min-w-[44px]' : ''
                              }`}
                              title="复制消息"
                            >
                              <Copy className={`${
                                isMobile ? 'w-5 h-5' : 'w-4 h-4'
                              }`} />
                            </button>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>

                {message.role === 'user' && (
                  <div className={`${
                    isMobile ? 'w-7 h-7' : 'w-8 h-8'
                  } rounded-full bg-gray-100 flex items-center justify-center flex-shrink-0`}>
                    <User className={`${
                      isMobile ? 'w-3.5 h-3.5' : 'w-4 h-4'
                    } text-gray-600`} />
                  </div>
                )}
              </div>
                )
              })}

            {isLoading && !hasStartedStreaming && (
              <div className={`flex ${
                isMobile ? 'gap-2' : 'gap-3'
              } justify-start`}>
                <div className={`${
                  isMobile ? 'w-7 h-7' : 'w-8 h-8'
                } rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0`}>
                  <Bot className={`${
                    isMobile ? 'w-3.5 h-3.5' : 'w-4 h-4'
                  } text-blue-600`} />
                </div>
                <div className={`bg-white ${
                  isMobile ? 'px-3 py-2' : 'px-4 py-3'
                }`}>
                  <div className={`flex items-center gap-2 text-gray-500 ${
                    isMobile ? 'text-sm' : 'text-base'
                  }`}>
                    <Brain className={`${
                      isMobile ? 'w-3.5 h-3.5' : 'w-4 h-4'
                    } animate-pulse`} />
                    <span>正在思考...</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
          </div>

          {/* 滚动到底部按钮 */}
          {userHasScrolled && !isNearBottom && !(showArtifacts && isMobile) && (
            <div className={`absolute z-10 ${
              showArtifacts 
                ? 'bottom-20 right-8' // 桌面端artifacts展开时，按钮在聊天区域内，稍微调整位置避免与边界太近
                : 'bottom-20 right-4' // 未展开时的位置
            }`}>
              <button
                onClick={() => {
                  setUserHasScrolled(false)
                  scrollToBottom(true)
                }}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors duration-200"
              >
                <svg 
                  className="w-4 h-4" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                </svg>
                <span className="text-sm">滚动到底部</span>
              </button>
            </div>
          )}

          {/* 固定在底部的输入区域 */}
          <div className="sticky bottom-0 flex-shrink-0 bg-white z-10">
            <div className={`${isMobile ? 'p-2' : showArtifacts ? 'p-4' : 'p-4 px-8'}`}>
        <ChatInput
          onSendMessage={sendMessage}
          isLoading={isLoading}
          settings={settings}
          onSettingsChange={setSettings}
        />
            </div>
          </div>
        </div>
      </div>

      {/* Artifacts 面板 */}
      {showArtifacts && (
        <div className={`${
          isMobile ? 'w-full h-screen' : 'w-1/2 h-screen'
        }`}>
          <ArtifactsDisplay
            artifacts={currentArtifacts}
            onClose={handleCloseArtifacts}
            streamingContent={streamingContent}
            isStreaming={isStreamingArtifacts}
          />
        </div>
      )}
    </div>
  )
}