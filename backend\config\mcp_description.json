{"amap-maps": "高德地图MCP服务器，可以查询地图信息、路线规划、位置搜索、POI查询、地址解析、距离测量、天气查询等地理位置相关功能", "airbnb": "Airbnb MCP服务器，可以搜索民宿房源、查询价格、获取房源详情、查看评价等住宿相关功能", "playwright": "Playwright MCP服务器，可以进行网页自动化操作、浏览器控制、网页截图、表单填写、链接点击、网页爬虫等网页交互功能", "edgeone-pages-mcp": "EdgeOne Pages MCP服务器，可以部署静态网页、发布HTML内容、网站托管等网页发布功能", "github": "GitHub MCP服务器，可以进行GitHub仓库操作、代码管理、Issue处理、Pull Request操作、文件读写、分支管理等代码协作功能", "fetch": "Fetch MCP服务器，可以进行网页内容抓取、API调用、数据获取、网络请求等数据抓取功能", "302ai-sandbox-mcp": "302AI沙盒MCP服务器，可以运行代码、执行命令、创建文件、进程管理等代码执行环境功能", "quickchart-server": "QuickChart MCP服务器，可以生成图表、数据可视化、绘制统计图、创建图形等图表生成功能", "howtocook-mcp": "HowToCook MCP服务器，可以查询菜谱、烹饪方法、食材搭配、烹饪技巧等烹饪相关功能", "exa": "Exa Search MCP服务器，可以进行网页搜索、学术论文检索、公司与竞争对手信息抓取、GitHub／Wikipedia查询等实时信息检索功能"}