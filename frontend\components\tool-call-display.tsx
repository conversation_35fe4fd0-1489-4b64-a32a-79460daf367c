'use client'

import { useState } from 'react'
import { Ch<PERSON>ronRight, CheckCircle, Clock, Loader2, XCircle } from 'lucide-react'

interface ToolCallDisplayProps {
  toolName: string
  parameters?: Record<string, unknown>
  result?: Record<string, unknown> | string | number | boolean | null
  status: 'calling' | 'success' | 'error'
  error?: string
}

export default function ToolCallDisplay({ toolName, parameters, result, status, error }: ToolCallDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const getStatusIcon = () => {
    switch (status) {
      case 'calling':
        return <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin text-blue-500" />
      case 'success':
        return <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-green-500" />
      case 'error':
        return <XCircle className="w-3 h-3 sm:w-4 sm:h-4 text-red-500" />
      default:
        return <Clock className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500" />
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'calling':
        return 'border-blue-200 bg-blue-50'
      case 'success':
        return 'border-green-200 bg-green-50'
      case 'error':
        return 'border-red-200 bg-red-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const formatJson = (obj: Record<string, unknown> | string | number | boolean | null) => {
    if (typeof obj === 'string' || typeof obj === 'number' || typeof obj === 'boolean' || obj === null) {
      return JSON.stringify(obj, null, 2)
    }
    return JSON.stringify(obj, null, 2)
  }

  return (
    <div className={`border rounded-lg ${getStatusColor()} transition-all duration-200 w-full max-w-full`}>
      <div 
        className="flex items-center justify-between p-2 sm:p-3 cursor-pointer hover:bg-opacity-80 w-full active:bg-opacity-90 transition-all"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-1.5 sm:gap-2 min-w-0 flex-1">
          {getStatusIcon()}
          <span className="text-xs sm:text-sm font-medium text-gray-800 truncate">
            Called MCP tool
          </span>
          <span className="bg-blue-100 text-blue-800 text-[10px] sm:text-xs px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full font-mono flex-shrink-0">
            {toolName}
          </span>
        </div>
        <div className="flex items-center gap-1 sm:gap-2 flex-shrink-0">
          <span className="text-[10px] sm:text-xs text-gray-500 capitalize hidden sm:inline">{status}</span>
          <ChevronRight 
            className={`w-3 h-3 sm:w-4 sm:h-4 text-gray-400 transition-transform ${isExpanded ? 'rotate-90' : ''}`} 
          />
        </div>
      </div>
      
      {/* 展开后的内容 */}
      {isExpanded && (
        <div className="border-t border-gray-200 p-2 sm:p-3 space-y-2 sm:space-y-3 bg-white w-full max-w-full overflow-hidden">
          {parameters && (
            <div className="w-full max-w-full">
              <h4 className="text-[10px] sm:text-xs font-semibold text-gray-600 mb-1 sm:mb-2">Parameters:</h4>
              <div className="w-full max-w-full overflow-hidden">
                <pre className="bg-gray-800 text-green-400 p-2 sm:p-3 rounded text-[10px] sm:text-xs overflow-x-auto font-mono border w-full max-w-full break-words">
                  <code className="block whitespace-pre-wrap break-words">{formatJson(parameters)}</code>
                </pre>
              </div>
            </div>
          )}
          
          {result && (
            <div className="w-full max-w-full">
              <h4 className="text-[10px] sm:text-xs font-semibold text-gray-600 mb-1 sm:mb-2">Result:</h4>
              <div className="w-full max-w-full overflow-hidden">
                <pre className="bg-gray-800 text-green-400 p-2 sm:p-3 rounded text-[10px] sm:text-xs overflow-x-auto font-mono border w-full max-w-full break-words">
                  <code className="block whitespace-pre-wrap break-words">{formatJson(result)}</code>
                </pre>
              </div>
            </div>
          )}
          
          {error && (
            <div className="w-full max-w-full">
              <h4 className="text-[10px] sm:text-xs font-semibold text-red-600 mb-1 sm:mb-2">Error:</h4>
              <div className="w-full max-w-full overflow-hidden">
                <pre className="bg-red-50 text-red-800 p-2 sm:p-3 rounded text-[10px] sm:text-xs overflow-x-auto font-mono border border-red-200 w-full max-w-full break-words">
                  <code className="block whitespace-pre-wrap break-words">{error}</code>
                </pre>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
} 