#!/usr/bin/env python3

import asyncio
from autogen_agentchat.agents import Assistant<PERSON><PERSON>
from autogen_ext.tools.mcp import McpWorkbench, mcp_server_tools
from agent.utils import get_mcp_server_params
from agent.llm_service import ModelManager

async def test_events():
    """测试autogen事件类型"""
    
    # 创建模型客户端
    model_manager = ModelManager()
    model_client = model_manager.create_client("gpt-4.1")
    
    # 创建MCP工作台
    server_params = get_mcp_server_params("amap-maps")
    tools = await mcp_server_tools(server_params)
    workbench = McpWorkbench(server_params)
    await workbench.__aenter__()
    
    # 创建Agent
    agent = AssistantAgent(
        name="test_agent",
        model_client=model_client,
        system_message=f"你是一个AI助手，可以使用{tools}来回答问题。",
        workbench=workbench,
        reflect_on_tool_use=True,
        model_client_stream=True,
    )
    
    print("开始测试工具调用事件...")
    
    # 运行流式任务
    async for chunk in agent.run_stream(task="请查询北京的天气情况"):
        chunk_type = getattr(chunk, 'type', '')
        print(f"事件类型: {chunk_type}")
        print(f"事件对象: {type(chunk)}")
        print(f"事件属性: {[attr for attr in dir(chunk) if not attr.startswith('_')]}")
        
        if hasattr(chunk, 'content'):
            print(f"内容: {chunk.content}")
        
        print("-" * 50)
        
        # 只显示前几个事件
        if chunk_type == 'TextMessage':
            break
    
    # 清理
    await workbench.__aexit__(None, None, None)

if __name__ == "__main__":
    asyncio.run(test_events()) 

    # cd backend && python -m playground.test_events

    