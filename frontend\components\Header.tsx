'use client'

import React from 'react'
import Image from 'next/image'
import { User, Settings, LogOut } from 'lucide-react'

interface HeaderProps {
  userName?: string
  userAvatar?: string
  onSettingsClick?: () => void
  onLogoutClick?: () => void
}

export default function Header({ 
  userName = "用户", 
  userAvatar, 
  onSettingsClick, 
  onLogoutClick 
}: HeaderProps) {
  const [showUserMenu, setShowUserMenu] = React.useState(false)
  const [isScrolled, setIsScrolled] = React.useState(false)
  const [isMobile, setIsMobile] = React.useState(false)

  // 检测移动端
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // 监听滚动事件
  React.useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <>
      {/* 添加shimmer动画的CSS */}
      <style jsx global>{`
        .shimmer-container {
          position: relative;
          overflow: hidden;
        }
        
        .shimmer-effect {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent
          );
          animation: shimmer 2s infinite;
          z-index: 5;
        }
        
        @keyframes shimmer {
          0% {
            left: -100%;
          }
          100% {
            left: 100%;
          }
        }
      `}</style>

      <header className={`${
        isMobile ? 'px-4 h-14' : 'px-50 h-12'
      } flex items-center justify-between relative z-10 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-md shadow-sm'
          : 'bg-white'
      }`}>
        {/* Left - Logo */}
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <div className={`${
            isMobile ? 'w-9 h-9' : 'w-8 h-8'
          } rounded-lg flex items-center justify-center`}>
            <Image 
              src="/logo.png" 
              alt="MCP AutoGen" 
              width={isMobile ? 24 : 20} 
              height={isMobile ? 24 : 20}
              className={`${
                isMobile ? 'w-9 h-9' : 'w-8 h-8'
              } rounded-lg transition-transform duration-300 hover:scale-110`}
            />
          </div>
          <div>
            <h1 className={`${
              isMobile ? 'text-xl' : 'text-lg'
            } font-extrabold text-black`}>
              Zenus
            </h1>
          </div>
        </div>

        {/* Right - User Avatar */}
        <div className="relative">
          <button
            onClick={() => setShowUserMenu(!showUserMenu)}
            className={`relative flex ${
              isMobile ? 'h-10 w-10' : 'h-9 w-9'
            } cursor-pointer items-center justify-center overflow-hidden rounded-full bg-indigo-100 shimmer-container hover:ring-2 hover:ring-purple-300 transition-all duration-200 ${
              isMobile ? 'min-h-[40px] min-w-[40px]' : ''
            }`}
          >
            {userAvatar ? (
              <>
                <Image
                  src={userAvatar}
                  alt={userName}
                  fill
                  className="object-cover z-10"
                />
                <span className="shimmer-effect"></span>
              </>
            ) : (
              <>
                <User className={`${
                  isMobile ? 'w-6 h-6' : 'w-5 h-5'
                } text-black z-10`} />
                <span className="shimmer-effect"></span>
              </>
            )}
          </button>

          {/* User Menu Dropdown */}
          {showUserMenu && (
            <>
              {/* Backdrop */}
              <div 
                className="fixed inset-0 z-10" 
                onClick={() => setShowUserMenu(false)}
              />
              
              {/* Menu */}
              <div className={`absolute ${
                isMobile ? 'right-0 top-full mt-2 w-52' : 'right-0 top-full mt-2 w-48'
              } bg-white/95 backdrop-blur-md rounded-lg shadow-xl border border-gray-200 py-1 z-20`}>
                <div className={`${
                  isMobile ? 'px-4 py-3' : 'px-3 py-2'
                } border-b border-gray-100`}>
                  <p className={`${
                    isMobile ? 'text-base' : 'text-sm'
                  } font-medium text-gray-900`}>{userName}</p>
                  <p className={`${
                    isMobile ? 'text-sm' : 'text-xs'
                  } text-gray-500`}>在线</p>
                </div>
                
                {onSettingsClick && (
                  <button
                    onClick={() => {
                      onSettingsClick()
                      setShowUserMenu(false)
                    }}
                    className={`w-full flex items-center gap-2 ${
                      isMobile ? 'px-4 py-3 text-base' : 'px-3 py-2 text-sm'
                    } text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors ${
                      isMobile ? 'min-h-[44px]' : ''
                    }`}
                  >
                    <Settings className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
                    设置
                  </button>
                )}
                
                {onLogoutClick && (
                  <button
                    onClick={() => {
                      onLogoutClick()
                      setShowUserMenu(false)
                    }}
                    className={`w-full flex items-center gap-2 ${
                      isMobile ? 'px-4 py-3 text-base' : 'px-3 py-2 text-sm'
                    } text-gray-700 hover:bg-red-50 hover:text-red-600 transition-colors ${
                      isMobile ? 'min-h-[44px]' : ''
                    }`}
                  >
                    <LogOut className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
                    退出登录
                  </button>
                )}
              </div>
            </>
          )}
        </div>
      </header>
    </>
  )
}
