'use client'

import React, { useState, useRef, useEffect, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Copy, Download, Code, Eye, Upload, X, Maximize, Minimize } from 'lucide-react'
import toast from 'react-hot-toast'
import <PERSON><PERSON><PERSON>w<PERSON><PERSON><PERSON> from '@/lib/Renderer/CodeViewRenderer'
import ChatInterface from '@/components/chat-interface'
import Sidebar from '@/components/Sidebar'

// 页面数据类型
interface PageData {
  id: string
  title: string
  content: string
  fileName: string
}

// 初始模拟数据
const initialMockData: PageData[] = [
  {
    id: '1',
    title: "Iceland's Natural Wonders",
    fileName: "page1.html",
    content: `<!DOCTYPE html><html lang="en"><head>
<meta charset="utf-8">
<meta content="width=device-width, initial-scale=1.0" name="viewport">
<title>Iceland's Natural Wonders</title>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700&amp;family=Open+Sans:wght@400;600&amp;display=swap" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
<style>
body, html {
  margin: 0;
  padding: 0;
  font-family: 'Open Sans', sans-serif;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  width: 800px;
  height: 450px;
  overflow: hidden;
}
.hero-section {
  background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.6)), url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3') center/cover;
  width: 800px;
  height: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
}
.title {
  font-family: 'Montserrat', sans-serif;
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}
.subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}
.icons-container {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}
.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255,255,255,0.1);
  padding: 1rem;
  border-radius: 50%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}
.icon-item i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}
.icon-label {
  font-size: 0.8rem;
  font-weight: 600;
}
.presentation-label {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  background: rgba(255,255,255,0.1);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  font-size: 0.8rem;
}
</style>
</head>
<body>
<div class="hero-section">
  <h1 class="title">Iceland's Natural Wonders</h1>
  <p class="subtitle">A Journey Through Breathtaking Landscapes and Wildlife</p>
  <div class="icons-container">
    <div class="icon-item">
      <i class="fas fa-moon"></i>
      <span class="icon-label">Northern Lights</span>
    </div>
    <div class="icon-item">
      <i class="fas fa-mountain"></i>
      <span class="icon-label">Glaciers</span>
    </div>
    <div class="icon-item">
      <i class="fas fa-dove"></i>
      <span class="icon-label">Puffins</span>
    </div>
    <div class="icon-item">
      <i class="fas fa-water"></i>
      <span class="icon-label">Whales</span>
    </div>
  </div>
  <div class="presentation-label">Tourism Marketing Presentation</div>
</div>
</body>
</html>`
  },
  {
    id: '2',
    title: "Journey Through Iceland's Natural Treasures",
    fileName: "page2.html",
    content: `<!DOCTYPE html><html lang="en"><head>
<meta charset="utf-8">
<meta content="width=device-width, initial-scale=1.0" name="viewport">
<title>Journey Through Iceland's Natural Treasures</title>
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700&amp;family=Open+Sans:wght@400;600&amp;display=swap" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
<style>
body, html {
  margin: 0;
  padding: 0;
  font-family: 'Open Sans', sans-serif;
  background: linear-gradient(135deg, #2c5282 0%, #2d3748 100%);
  width: 800px;
  height: 450px;
  overflow: hidden;
}
.container {
  width: 800px;
  height: 450px;
  padding: 2rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.header {
  text-align: center;
  color: white;
  margin-bottom: 2rem;
}
.main-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}
.subtitle {
  font-size: 1rem;
  opacity: 0.9;
}
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  flex: 1;
}
.feature-card {
  background: rgba(255,255,255,0.1);
  border-radius: 15px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  color: white;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: transform 0.3s ease;
}
.feature-card:hover {
  transform: translateY(-5px);
}
.feature-icon {
  width: 50px;
  height: 50px;
  background: rgba(74, 222, 128, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: #4ade80;
}
.feature-content h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}
.feature-content p {
  opacity: 0.9;
  line-height: 1.4;
  font-size: 0.9rem;
}
.location-tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  opacity: 0.8;
  font-size: 0.8rem;
}
</style>
</head>
<body>
<div class="container">
  <div class="header">
    <h1 class="main-title">Journey Through Iceland's Natural Treasures</h1>
    <p class="subtitle">Embark on an unforgettable adventure exploring Iceland's most breathtaking natural phenomena across the island's most spectacular viewing locations.</p>
  </div>
  
  <div class="content-grid">
    <div class="feature-card">
      <div class="feature-icon">
        <i class="fas fa-moon"></i>
      </div>
      <div class="feature-content">
        <h3>Northern Lights</h3>
        <p>Experience the mesmerizing dance of the aurora borealis illuminating Iceland's night skies.</p>
        <div class="location-tag">
          <i class="fas fa-map-marker-alt"></i>
          <span>Via Gullfoss Iceland and Skaftafell National Park</span>
        </div>
      </div>
    </div>
    
    <div class="feature-card">
      <div class="feature-icon">
        <i class="fas fa-mountain"></i>
      </div>
      <div class="feature-content">
        <h3>Glacial Majesty</h3>
        <p>Discover Europe's largest ice cap and its stunning ice caves, lagoons, and formations.</p>
        <div class="location-tag">
          <i class="fas fa-map-marker-alt"></i>
          <span>Vatnajökull Glacier, covering 8% of Iceland</span>
        </div>
      </div>
    </div>
  </div>
</div>
</body>
</html>`
  }
]

export default function TestPPTXCanvas() {
  // PPTX related states
  const [pages] = useState<PageData[]>(initialMockData)
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('preview')
  const [currentPage, setCurrentPage] = useState(0)
  const [isFullScreen, setIsFullScreen] = useState(false)
  const canvasScrollRef = useRef<HTMLDivElement>(null)
  const pageRefs = useRef<(HTMLDivElement | null)[]>([])

  // Sidebar state
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true)

  // Sidebar toggle function
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  // 监听滚动位置，更新当前页面
  useEffect(() => {
    const handleScroll = () => {
      if (!canvasScrollRef.current || pages.length === 0) return

      const containerHeight = canvasScrollRef.current.clientHeight

      // 找到当前在视窗中心的页面
      for (let i = 0; i < pageRefs.current.length; i++) {
        const pageElement = pageRefs.current[i]
        if (pageElement) {
          const rect = pageElement.getBoundingClientRect()
          const containerRect = canvasScrollRef.current!.getBoundingClientRect()
          
          // 如果页面的中心在视窗中心附近
          if (rect.top <= containerRect.top + containerHeight / 2 && 
              rect.bottom >= containerRect.top + containerHeight / 2) {
            setCurrentPage(i)
            break
          }
        }
      }
    }

    const scrollContainer = canvasScrollRef.current
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll)
      return () => scrollContainer.removeEventListener('scroll', handleScroll)
    }
  }, [pages.length])

  // 复制功能
  const copyToClipboard = async () => {
    if (pages.length === 0) return
    try {
      await navigator.clipboard.writeText(pages[currentPage].content)
      toast.success('已复制到剪贴板')
    } catch {
      toast.error('复制失败')
    }
  }

  // 下载功能
  const downloadPage = () => {
    if (pages.length === 0) return
    const currentPageData = pages[currentPage]
    const blob = new Blob([currentPageData.content], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = currentPageData.fileName
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('页面已下载')
  }

  // 全屏切换
  const toggleFullScreen = useCallback(async () => {
    try {
      if (!isFullScreen) {
        // 进入全屏
        const element = document.documentElement
        if (element.requestFullscreen) {
          await element.requestFullscreen()
        } else if ((element as Document['documentElement'] & { webkitRequestFullscreen?: () => Promise<void> }).webkitRequestFullscreen) {
          await (element as Document['documentElement'] & { webkitRequestFullscreen: () => Promise<void> }).webkitRequestFullscreen()
        } else if ((element as Document['documentElement'] & { msRequestFullscreen?: () => Promise<void> }).msRequestFullscreen) {
          await (element as Document['documentElement'] & { msRequestFullscreen: () => Promise<void> }).msRequestFullscreen()
        }
        setIsFullScreen(true)
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          await document.exitFullscreen()
        } else if ((document as Document & { webkitExitFullscreen?: () => Promise<void> }).webkitExitFullscreen) {
          await (document as Document & { webkitExitFullscreen: () => Promise<void> }).webkitExitFullscreen()
        } else if ((document as Document & { msExitFullscreen?: () => Promise<void> }).msExitFullscreen) {
          await (document as Document & { msExitFullscreen: () => Promise<void> }).msExitFullscreen()
        }
        setIsFullScreen(false)
      }
    } catch (error) {
      console.log('全屏切换失败:', error)
      // 降级到简单的布局切换
      setIsFullScreen(!isFullScreen)
    }
  }, [isFullScreen])

  // 监听浏览器全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = 
        document.fullscreenElement ||
        (document as Document & { webkitFullscreenElement?: Element }).webkitFullscreenElement ||
        (document as Document & { msFullscreenElement?: Element }).msFullscreenElement
      
      setIsFullScreen(!!isCurrentlyFullscreen)
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullScreen) {
        toggleFullScreen()
      }
      if (event.key === 'F11') {
        event.preventDefault()
        toggleFullScreen()
      }
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('msfullscreenchange', handleFullscreenChange)
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.removeEventListener('msfullscreenchange', handleFullscreenChange)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isFullScreen, toggleFullScreen])

  // 关闭页面
  const handleClose = () => {
    if (confirm('确定要关闭PPTX预览吗？')) {
      window.close()
    }
  }

  // 渲染预览（滚动模式）
  const renderPreview = () => {
    if (pages.length === 0) {
      return (
        <div className="h-full flex items-center justify-center bg-gray-100">
          <div className="text-center text-gray-500">
            <Upload className="w-16 h-16 mx-auto mb-4 text-gray-300" />
            <p className="text-lg">请先上传HTML文件</p>
          </div>
        </div>
      )
    }

    return (
      <div 
        ref={canvasScrollRef}
        className="h-full overflow-auto bg-gray-100 p-6"
        style={{ scrollBehavior: 'smooth' }}
      >
        <div className="space-y-6">
          {pages.map((page, index) => (
            <div
              key={page.id}
              ref={(el) => {
                pageRefs.current[index] = el
              }}
              className="flex justify-center"
            >
              <div 
                className="bg-white shadow-2xl"
                style={{ 
                  width: '800px',
                  height: '450px'
                }}
              >
                <iframe
                  srcDoc={page.content}
                  className="w-full h-full border-none"
                  sandbox="allow-scripts allow-same-origin"
                  title={`PPTX Preview - ${page.title}`}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  // 渲染代码视图（滚动模式）
  const renderCodeView = () => {
    if (pages.length === 0) {
      return (
        <div className="h-full flex items-center justify-center bg-gray-900">
          <div className="text-center text-gray-400">
            <Code className="w-16 h-16 mx-auto mb-4" />
            <p className="text-lg">请先上传HTML文件</p>
          </div>
        </div>
      )
    }

    return (
      <div className="h-full overflow-auto bg-white">
        <div className="space-y-4 p-4">
          {pages.map((page, index) => (
            <div key={page.id} className="bg-gray-800 rounded-lg overflow-hidden">
              <div className="bg-gray-700 px-4 py-2 text-white text-sm font-medium">
                页面 {index + 1}: {page.title}
              </div>
              <div className="h-96 overflow-auto">
                <CodeViewRenderer 
                  code={page.content}
                  language="html"
                  height="100%"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`h-screen bg-gray-50 flex transition-all duration-500 ease-in-out ${
      isFullScreen ? 'bg-white' : ''
    }`}>
      {!isFullScreen && (
        <>
          {/* 左侧边栏 */}
          <div className="transition-all duration-500 ease-in-out">
            <Sidebar isCollapsed={sidebarCollapsed} onToggle={toggleSidebar} />
          </div>

          {/* 中间聊天界面 */}
          <div className={`bg-white border-r border-gray-200 transition-all duration-500 ease-in-out ${
            sidebarCollapsed ? 'flex-1' : 'w-1/2'
          }`}>
            <ChatInterface />
          </div>
        </>
      )}

      {/* 右侧PPTX预览区域 */}
      <div className={`bg-white flex flex-col transition-all duration-500 ease-in-out ${
        isFullScreen ? 'w-full h-full' : sidebarCollapsed ? 'w-1/2' : 'flex-1'
      }`}>

        {/* Canvas头部工具栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
          <div className="flex items-center space-x-3">
            <h1 className="text-lg font-semibold">预览器</h1>
            {pages.length > 0 && (
              <span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">
                页面 {currentPage + 1} / {pages.length}
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {/* 预览/代码切换 */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <Button
                variant={viewMode === 'preview' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('preview')}
                className="px-3 py-1 h-8"
                disabled={pages.length === 0}
              >
                <Eye className="w-4 h-4 mr-1" />
                预览
              </Button>
              <Button
                variant={viewMode === 'code' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('code')}
                className="px-3 py-1 h-8"
                disabled={pages.length === 0}
              >
                <Code className="w-4 h-4 mr-1" />
                代码
              </Button>
            </div>

            {/* 操作按钮 */}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={copyToClipboard}
              disabled={pages.length === 0}
            >
              <Copy className="w-4 h-4" />
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={downloadPage}
              disabled={pages.length === 0}
            >
              <Download className="w-4 h-4" />
            </Button>

            {/* 全屏按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullScreen}
              disabled={pages.length === 0}
              className="flex items-center space-x-1"
              title={isFullScreen ? "退出全屏" : "进入全屏"}
            >
              {isFullScreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
            </Button>

            {/* 关闭按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleClose}
              className="flex items-center space-x-1 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* 页面列表和预览 */}
        <div className="flex-1 overflow-hidden flex">
          {/* 右侧预览内容 */}
          <div className="flex-1 overflow-hidden">
            {viewMode === 'preview' ? renderPreview() : renderCodeView()}
          </div>
        </div>
      </div>
    </div>
  )
}
