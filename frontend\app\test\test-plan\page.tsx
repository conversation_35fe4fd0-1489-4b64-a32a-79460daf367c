'use client'

import ExecutionPlanDisplay from '@/components/execution-plan-display'
import { useState } from 'react'

// Import the type from the component
type ExecutionPlan = {
  use_mcp: 'yes' | 'no'
  reason: string
  plan: Array<{
    step: number
    action: 'call_mcp' | 'direct_response' | 'summary'
    server?: string
    description: string
    status?: 'pending' | 'executing' | 'completed' | 'skipped'
  }>
}

const samplePlan: ExecutionPlan = {
  use_mcp: 'yes',
  reason: '需要搜索问M7的销售数据和竞争对手信息，这些都是实时变化的市场数据，需要使用MCP服务器进行网络搜索',
  plan: [
    {
      step: 1,
      action: 'call_mcp' as const,
      description: '搜索问M7未来各大区销售数据',
      server: 'exa',
      status: 'pending' as const
    },
    {
      step: 2,
      action: 'call_mcp' as const,
      description: '搜索竞争对手销售数据',
      server: 'exa',
      status: 'pending' as const
    },
    {
      step: 3,
      action: 'call_mcp' as const,
      description: '搜索汽车行业营销趋势',
      server: 'exa',
      status: 'pending' as const
    },
    {
      step: 4,
      action: 'direct_response' as const,
      description: '分析销售数据并生成可视化图表',
      status: 'pending' as const
    },
    {
      step: 5,
      action: 'summary' as const,
      description: '综合分析并提出营销建议',
      status: 'pending' as const
    }
  ]
}

export default function TestPlanPage() {
  const [plan, setPlan] = useState(samplePlan)
  const [currentStep, setCurrentStep] = useState<number | undefined>(undefined)

  const handlePlanChange = (updatedPlan: ExecutionPlan) => {
    setPlan(updatedPlan)
    console.log('Plan updated:', updatedPlan)
  }

  const handleConfirmExecution = (planToExecute: ExecutionPlan) => {
    console.log('Executing plan:', planToExecute)
    alert('开始执行任务！')
    // 模拟执行过程
    setCurrentStep(1)
    setTimeout(() => setCurrentStep(2), 2000)
    setTimeout(() => setCurrentStep(3), 4000)
    setTimeout(() => setCurrentStep(4), 6000)
    setTimeout(() => setCurrentStep(5), 8000)
    setTimeout(() => setCurrentStep(undefined), 10000)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-3xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">Compact Task Cards</h1>
          <p className="text-gray-600 text-sm">Shadcn-ui powered, compact design with proper cursors</p>
        </div>

        <div className="space-y-4">
          {/* 可编辑版本 */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-3">Editable Version</h2>
            <ExecutionPlanDisplay
              plan={plan}
              isEditable={true}
              onPlanChange={handlePlanChange}
              onConfirmExecution={handleConfirmExecution}
            />
          </div>

          {/* 执行中版本 */}
          <div>
            <h2 className="text-lg font-medium text-gray-900 mb-3">Execution Progress</h2>
            <ExecutionPlanDisplay
              plan={plan}
              currentExecutingStep={currentStep}
              isEditable={false}
            />
          </div>

          {/* 控制按钮 */}
          <div className="flex gap-3">
            <button
              onClick={() => setCurrentStep(undefined)}
              className="px-3 py-1.5 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm cursor-pointer"
            >
              Reset
            </button>
            <button
              onClick={() => setCurrentStep(1)}
              className="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm cursor-pointer"
            >
              Simulate
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
