# Zenus: Next-Gen AI Agent

**English** | [中文](README-CN.md)

Zenus is a multi-agent chatbot that can help you with your tasks.

## Table of Contents

- [Features](#features)
- [Getting Started](#getting-started)
  - [Prerequisites](#prerequisites)
  - [Running the Application](#running-the-application)
- [Testing](#testing)
- [Docker Deployment](#docker-deployment)
- [Contributing](#contributing)
- [License](#license)

## Features

- 🤖 **Multi-Agent Architecture**: Planner + Agent execution model
- 🧠 **Smart Task Planning**: AI-powered task analysis and distribution  
- 🔧 **MCP Integration**: Support for multiple MCP servers (GitHub, Amap, Airbnb, Playwright)
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile
- ⚡ **Real-time Streaming**: Live chat with streaming responses
- 🎨 **Artifacts Display**: Interactive preview for HTML and React components

## Getting Started

### Prerequisites

1. Frontend
> Install [npm](https://nodejs.org)

```bash
# Install dependencies
cd frontend
npm install

# Configure backend URL
cp .env.example .env.local
```

2. Backend
> Install [miniconda](https://www.anaconda.com/docs/getting-started/miniconda/install)
```bash
# Create an isolated Python environment using miniconda
conda create -n zenus python==3.11 -y
conda activate zenus

# Install dependencies
cd backend
pip install -r requirements.txt
```

3. Configure backend URL:
```bash
cd frontend
cp .env.example .env.local
```

### Running the Application

Use the provided startup script to launch both frontend and backend services:
```bash
make dev
```

## Docker Deployment

```bash
docker build -t zenus:v1.0.0 . 
docker run -p 3000:3000 -p 8000:8000 zenus:v1.0.0
```

## Contributing

Pull requests and issues are welcome to help improve Zenus!

## License

MIT License