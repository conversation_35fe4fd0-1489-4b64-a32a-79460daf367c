'use client'

import React, { useState, useEffect, useRef } from 'react'
import { X, User, Settings, MessageSquare, LogOut, HelpCircle, PanelLeft, ArrowLeftToLine } from 'lucide-react'
import Image from 'next/image'
import { useRouter, usePathname } from 'next/navigation'

interface SidebarProps {
  isCollapsed: boolean
  onToggle: () => void
  onNavigate?: (itemId: string) => void
}

export default function Sidebar({ isCollapsed, onToggle, onNavigate }: SidebarProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [activeItem, setActiveItem] = useState('chat')
  const [isMobile, setIsMobile] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [isHoveringToggle, setIsHoveringToggle] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // 点击外部关闭用户菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // 导航菜单项
  const menuItems = [
    { id: 'chat', label: 'Chats', iconType: 'svg', iconSrc: '/chats.svg', route: '/' },
    { id: 'pptx-canvas', label: 'Slides', iconType: 'svg', iconSrc: '/slides.png', route: '/test/test-pptx-canvas' },
    { id: 'html2pptx', label: 'HTML2PPTX', iconType: 'png', iconSrc: '/html2pptx.png', route: '/test/test-html2pptx' },
    { id: 'excel-canvas', label: 'Sheets', iconType: 'svg', iconSrc: '/sheets.svg', route: '/test/test-excel-canvas' },
    { id: 'mermaid', label: 'Mermaid', iconType: 'svg', iconSrc: '/charts.svg', route: '/test/test-mermaid' },
    { id: 'sandbox', label: 'Sandbox', iconType: 'svg', iconSrc: '/sandbox.png', route: '/test/test-sandbox' },
    { id: 'webpage', label: 'Apps', iconType: 'svg', iconSrc: '/webpage.png', route: '/test/test-apps' }
  ]

  // 根据当前路径设置活跃菜单项
  useEffect(() => {
    const currentItem = menuItems.find(item => item.route === pathname)
    if (currentItem && currentItem.id !== activeItem) {
      setActiveItem(currentItem.id)
    }
  }, [pathname, activeItem]) // eslint-disable-line react-hooks/exhaustive-deps

  // 用户菜单项
  const userMenuItems = [
    { id: 'settings', label: 'Settings', icon: Settings },
    { id: 'help', label: 'Help & Support', icon: HelpCircle },
    { id: 'logout', label: 'Log out', icon: LogOut }
  ]

  const handleUserMenuClick = () => {
    setShowUserMenu(!showUserMenu)
  }

  const handleUserMenuItemClick = (itemId: string) => {
    console.log(`User menu item clicked: ${itemId}`)
    setShowUserMenu(false)
    // 这里可以添加具体的处理逻辑
  }

  // 移动端覆盖层
  if (isMobile) {
    return (
      <>
        {/* 移动端覆盖背景 */}
        {!isCollapsed && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={onToggle}
          />
        )}
        
        {/* 移动端侧边栏 */}
        <div className={`fixed left-0 top-0 h-full bg-white shadow-xl z-50 transition-transform duration-300 ease-in-out ${
          isCollapsed ? '-translate-x-full' : 'translate-x-0'
        } w-60`}>
          {/* 顶部：品牌和关闭按钮 */}
          <div className="flex items-center justify-between p-2 border-b border-gray-100">
            <div className="flex items-center space-x-2">
              <div className="w-7 h-7 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center shadow-md">
                <MessageSquare className="w-3.5 h-3.5 text-white" />
              </div>
              <span className="font-bold text-gray-900 text-base">Zenus</span>
            </div>
            <button
              onClick={onToggle}
              className="p-1 hover:bg-gray-100 rounded-lg transition-all duration-200 cursor-pointer"
            >
              <X className="w-3.5 h-3.5 text-gray-600" />
            </button>
          </div>

          {/* 新对话按钮 */}
          <div className="p-2 border-b border-gray-100">
            <button className="w-full flex items-center space-x-2.5 px-3 py-2.5 text-gray-700 hover:bg-gray-100 rounded-lg transition-all duration-200 cursor-pointer">
              <div className="w-4 h-4 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                <Image 
                  src="/newchat.svg" 
                  alt="New Chat"
                  width={20} 
                  height={20}
                  className="filter invert brightness-0 contrast-100"
                />
              </div>
              <span className="font-medium text-sm">New Chat</span>
            </button>
          </div>

          {/* 导航菜单 */}
          <div className="flex-1 py-2 overflow-y-auto">
            <nav className="space-y-1 px-2">
              {menuItems.map((item) => {
                const isActive = activeItem === item.id
                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      // 立即设置活跃状态，避免等待路由切换
                      if (activeItem !== item.id) {
                        setActiveItem(item.id)
                      }
                      onNavigate?.(item.id)
                      router.push(item.route)
                      onToggle() // 点击后关闭移动端侧边栏
                    }}
                    className={`w-full flex items-center space-x-2.5 px-2 py-1.5 rounded-lg transition-all duration-200 cursor-pointer ${
                      isActive
                        ? 'bg-blue-50 text-black-700 border border-blue-200 shadow-sm'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Image 
                      src={item.iconSrc} 
                      alt={item.label}
                      width={20} 
                      height={20}
                      className={`transition-colors duration-200 ${
                        isActive 
                          ? 'opacity-100' 
                          : 'opacity-70'
                      }`}
                    />
                    <span className="font-medium text-sm">{item.label}</span>
                  </button>
                )
              })}
            </nav>
          </div>

          {/* 底部：用户信息 */}
          <div className="border-t border-gray-100 p-2" ref={userMenuRef}>
            <div className="relative">
              <button
                onClick={handleUserMenuClick}
                className="w-full flex items-center space-x-2.5 p-1.5 hover:bg-gray-50 rounded-lg transition-all duration-200 cursor-pointer"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center shadow-md">
                  <User className="w-4.5 h-4.5 text-white" />
                </div>
                <div className="flex-1 min-w-0 text-left">
                  <p className="text-sm font-semibold text-gray-900 truncate">
                    User
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    <EMAIL>
                  </p>
                </div>
              </button>

              {/* 用户菜单 */}
              {showUserMenu && (
                <div className="absolute bottom-full left-1 mb-2 w-48 bg-white border border-gray-200 rounded-lg shadow-xl z-50">
                  <div className="py-1.5">
                    {userMenuItems.map((item) => {
                      const Icon = item.icon
                      return (
                        <button
                          key={item.id}
                          onClick={() => handleUserMenuItemClick(item.id)}
                          className="w-full flex items-center space-x-2.5 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
                        >
                          <Icon className="w-4 h-4 text-gray-500" />
                          <span>{item.label}</span>
                        </button>
                      )
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </>
    )
  }

  // 桌面端侧边栏
  return (
    <div className={`flex flex-col h-full bg-[#f8f8f7] border-r border-gray-200 transition-all duration-500 ease-in-out ${
      isCollapsed ? 'w-14' : 'w-60'
    } relative z-10`}>
      {/* 顶部：品牌和折叠按钮 */}
      <div className="flex items-center justify-between p-2">
        <div className={`flex items-center space-x-2 transition-all duration-500 ease-in-out overflow-hidden ${
          isCollapsed ? 'w-0 opacity-0' : 'w-auto opacity-100'
        }`}>
          <Image src="/logo.png" alt="Zenus" width={32} height={32} />
          <span className="font-bold text-gray-900 text-base whitespace-nowrap">Zenus</span>
        </div>
        <button
          onClick={onToggle}
          onMouseEnter={() => setIsHoveringToggle(true)}
          onMouseLeave={() => setIsHoveringToggle(false)}
          className="p-2 hover:bg-gray-100 rounded-lg transition-all duration-500 ease-in-out cursor-pointer"
          title={isCollapsed ? "展开侧边栏" : "折叠侧边栏"}
        >
          {isHoveringToggle ? (
            <ArrowLeftToLine 
              className={`w-5 h-5 text-gray-600 transition-transform duration-500 ease-in-out ${
                isCollapsed ? 'rotate-180' : ''
              }`} 
            />
          ) : (
            <PanelLeft className="w-5 h-5 text-gray-600 transition-all duration-500 ease-in-out" />
          )}
        </button>
      </div>

      {/* 新对话按钮 - 顶部 */}
      <div className="p-2">
        <button 
          className={`w-full flex items-center px-2 py-1.5 rounded-lg transition-all duration-300 ease-in-out cursor-pointer text-gray-700 hover:bg-gray-100 hover:text-gray-900 ${
            isCollapsed ? 'justify-center' : 'space-x-2.5'
          }`}
          title={isCollapsed ? "New Chat" : undefined}
        >
          <div className="w-6 h-6 bg-black rounded-full flex items-center justify-center flex-shrink-0">
            <Image 
              src="/newchat.svg" 
              alt="New Chat"
              width={10} 
              height={10}
              className="filter invert brightness-0 contrast-100"
            />
          </div>
          <span className={`font-medium text-sm transition-all duration-500 ease-in-out overflow-hidden whitespace-nowrap ${
            isCollapsed ? 'w-0 opacity-0' : 'w-auto opacity-100'
          }`}>
            New Chat
          </span>
        </button>
      </div>

      {/* 中间：导航菜单 */}
      <div className="flex-1 py-2 overflow-y-auto">
        <nav className="space-y-1 px-2">
          {menuItems.map((item) => {
            const isActive = activeItem === item.id
            return (
              <button
                key={item.id}
                onClick={() => {
                  // 立即设置活跃状态，避免等待路由切换
                  if (activeItem !== item.id) {
                    setActiveItem(item.id)
                  }
                  onNavigate?.(item.id)
                  router.push(item.route)
                }}
                className={`w-full flex items-center px-2 py-1.5 rounded-lg transition-all duration-300 ease-in-out group cursor-pointer ${
                  isActive
                    ? 'text-black hover:text-gray-900'
                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                } ${isCollapsed ? 'justify-center' : 'space-x-2.5'}`}
                style={isActive ? { backgroundColor: '#e5e5e5' } : {}}
                onMouseEnter={(e) => {
                  if (isActive) {
                    e.currentTarget.style.backgroundColor = '#d1d1d1'
                  }
                }}
                onMouseLeave={(e) => {
                  if (isActive) {
                    e.currentTarget.style.backgroundColor = '#e5e5e5'
                  }
                }}
                title={isCollapsed ? item.label : undefined}
              >
                <Image 
                  src={item.iconSrc} 
                  alt={item.label}
                  width={20} 
                  height={20}
                  className={`transition-all duration-300 ease-in-out ${
                    isActive 
                      ? 'opacity-100' 
                      : 'opacity-70 group-hover:opacity-90'
                  }`}
                />
                <span className={`font-medium text-sm transition-all duration-500 ease-in-out overflow-hidden whitespace-nowrap ${
                  isCollapsed ? 'w-0 opacity-0' : 'w-auto opacity-100'
                }`}>
                  {item.label}
                </span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* 底部：用户头像和信息 */}
      <div className="border-t border-gray-100 p-2" ref={userMenuRef}>
        <div className="relative">
          <button
            onClick={handleUserMenuClick}
            className={`${
              isCollapsed 
                ? 'w-full flex items-center justify-center p-1 hover:bg-gray-50 rounded-lg transition-all duration-300 ease-in-out cursor-pointer' 
                : 'w-full flex items-center space-x-2.5 p-1.5 hover:bg-gray-50 rounded-lg transition-all duration-300 ease-in-out cursor-pointer'
            }`}
            title={isCollapsed ? "用户菜单" : undefined}
          >
            {/* 头像在折叠状态下保持固定大小，不被挤压 */}
            <div className="w-8 h-8 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center shadow-md flex-shrink-0">
              <User className="w-4.5 h-4.5 text-white" />
            </div>
            <div className={`flex-1 min-w-0 text-left transition-all duration-500 ease-in-out overflow-hidden ${
              isCollapsed ? 'w-0 opacity-0' : 'w-auto opacity-100'
            }`}>
              <p className="text-sm font-semibold text-gray-900 truncate whitespace-nowrap">
                User
              </p>
              <p className="text-xs text-gray-500 truncate whitespace-nowrap">
                <EMAIL>
              </p>
            </div>
          </button>

          {/* 用户菜单 - 桌面端 */}
          {showUserMenu && (
            <div className={`absolute ${
              isCollapsed 
                ? 'left-full ml-2 bottom-1' // 折叠时在右侧显示
                : 'bottom-full left-1 mb-2'  // 展开时在上方显示
            } w-48 bg-white border border-gray-200 rounded-lg shadow-xl z-50`}>
              <div className="py-1.5">
                {userMenuItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <button
                      key={item.id}
                      onClick={() => handleUserMenuItemClick(item.id)}
                      className="w-full flex items-center space-x-2.5 px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
                    >
                      <Icon className="w-4 h-4 text-gray-500" />
                      <span>{item.label}</span>
                    </button>
                  )
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 