#!/usr/bin/env python3
"""
测试分步执行计划功能
"""

import asyncio
import json
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(__file__)
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from config.models import ExecutionPlan, PlanStep
from agent.llm_chat import LLMChat

async def test_step_by_step_execution():
    """测试分步执行计划功能"""
    
    # 创建一个测试执行计划
    test_plan = ExecutionPlan(
        use_mcp="yes",
        reason="测试分步执行功能",
        plan=[
            PlanStep(
                step=1,
                action="direct_response",
                description="介绍我们要开始的任务",
                status="pending"
            ),
            PlanStep(
                step=2,
                action="call_mcp",
                server="amap-maps",
                description="查询北京的天气信息",
                status="pending"
            ),
            PlanStep(
                step=3,
                action="call_mcp", 
                server="amap-maps",
                description="查询上海的天气信息",
                status="pending"
            ),
            PlanStep(
                step=4,
                action="summary",
                description="对比北京和上海的天气差异",
                status="pending"
            )
        ]
    )
    
    print("🧪 开始测试分步执行计划功能")
    print(f"📋 测试计划: {test_plan.reason}")
    print(f"📝 步骤数量: {len(test_plan.plan)}")
    print("=" * 60)
    
    # 模拟聊天历史
    chat_history = [
        {"role": "user", "content": "你好，我想了解一下天气情况", "timestamp": 1000},
        {"role": "assistant", "content": "好的，我可以帮您查询天气信息。您想了解哪些城市的天气呢？", "timestamp": 1001}
    ]
    
    original_message = "请帮我对比一下北京和上海的天气差异"
    
    # 测试分步执行计划
    try:
        print("\n🚀 开始分步执行计划...")
        
        step_number = 0
        
        async for chunk in LLMChat.execute_plan_step_by_step(
            plan=test_plan,
            mcp_names=["amap-maps"],
            model_name="deepseek-r1-0528",
            original_message=original_message,
            chat_history=chat_history
        ):
            # 解析并显示流式数据
            if chunk.startswith('data: '):
                try:
                    data = json.loads(chunk[6:])
                    chunk_type = data.get('type', 'unknown')
                    
                    if chunk_type == 'step_start':
                        step_number = data.get('step', 0)
                        action = data.get('action', 'unknown')
                        description = data.get('description', '')
                        print(f"\n🔄 开始步骤 {step_number} ({action}): {description}")
                        
                    elif chunk_type == 'step_complete':
                        step_num = data.get('step', step_number)
                        action = data.get('action', 'unknown')
                        print(f"✅ 完成步骤 {step_num} ({action})")
                        
                    elif chunk_type == 'message_delta':
                        content = data.get('content', '')
                        if content.strip():
                            print(content, end='', flush=True)
                        
                    elif chunk_type == 'tool_call_start':
                        tool_name = data.get('tool_name', 'unknown')
                        step = data.get('step', step_number)
                        print(f"\n🔧 步骤{step} 调用工具: {tool_name}")
                        
                    elif chunk_type == 'tool_call_result':
                        tool_call_id = data.get('tool_call_id', 'unknown')
                        is_error = data.get('is_error', False)
                        step = data.get('step', step_number)
                        if is_error:
                            print(f"\n❌ 步骤{step} 工具调用失败: {tool_call_id}")
                        else:
                            print(f"\n✅ 步骤{step} 工具调用成功: {tool_call_id}")
                        
                    elif chunk_type == 'end':
                        print(f"\n\n🎉 {data.get('content', '分步执行完成')}")
                        break
                        
                    elif chunk_type == 'error':
                        print(f"\n❌ 错误: {data.get('content')}")
                        break
                        
                except json.JSONDecodeError:
                    pass
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_comparison_with_original():
    """对比原版执行和分步执行的差异"""
    
    test_plan = ExecutionPlan(
        use_mcp="yes",
        reason="对比测试：查询天气信息",
        plan=[
            PlanStep(
                step=1,
                action="call_mcp",
                server="amap-maps",
                description="查询深圳天气",
                status="pending"
            ),
            PlanStep(
                step=2,
                action="summary",
                description="总结天气信息",
                status="pending"
            )
        ]
    )
    
    original_message = "请告诉我深圳的天气情况"
    
    print("\n" + "=" * 60)
    print("🔄 对比测试：原版执行 vs 分步执行")
    print("=" * 60)
    
    # 测试原版执行
    print("\n📊 原版执行模式:")
    print("-" * 30)
    try:
        async for chunk in LLMChat.execute_plan_stream(
            plan=test_plan,
            mcp_names=["amap-maps"],
            model_name="deepseek-r1-0528",
            original_message=original_message
        ):
            if chunk.startswith('data: '):
                try:
                    data = json.loads(chunk[6:])
                    chunk_type = data.get('type', 'unknown')
                    if chunk_type == 'message_delta':
                        content = data.get('content', '')
                        if content.strip():
                            print(content, end='', flush=True)
                    elif chunk_type == 'end':
                        print(f"\n✅ 原版执行完成")
                        break
                except json.JSONDecodeError:
                    pass
    except Exception as e:
        print(f"❌ 原版执行失败: {e}")
    
    print("\n\n📊 分步执行模式:")
    print("-" * 30)
    
    # 测试分步执行
    try:
        async for chunk in LLMChat.execute_plan_step_by_step(
            plan=test_plan,
            mcp_names=["amap-maps"],
            model_name="deepseek-r1-0528",
            original_message=original_message
        ):
            if chunk.startswith('data: '):
                try:
                    data = json.loads(chunk[6:])
                    chunk_type = data.get('type', 'unknown')
                    if chunk_type == 'step_start':
                        step_number = data.get('step', 0)
                        print(f"\n🔄 步骤{step_number}开始")
                    elif chunk_type == 'message_delta':
                        content = data.get('content', '')
                        if content.strip():
                            print(content, end='', flush=True)
                    elif chunk_type == 'step_complete':
                        step_number = data.get('step', 0)
                        print(f"\n✅ 步骤{step_number}完成")
                    elif chunk_type == 'end':
                        print(f"\n✅ 分步执行完成")
                        break
                except json.JSONDecodeError:
                    pass
    except Exception as e:
        print(f"❌ 分步执行失败: {e}")

if __name__ == "__main__":
    print("🧪 分步执行计划功能测试")
    print("=" * 60)
    
    # 运行基础测试
    asyncio.run(test_step_by_step_execution())
    
    # 运行对比测试
    asyncio.run(test_comparison_with_original())
    
    print("\n✅ 所有测试完成") 