'use client'

import React from 'react'
import ReactMarkdown from 'react-markdown'
import type { Components } from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import CodeBlockRenderer from '@/lib/Renderer/CodeBlockRenderer'
import MermaidRenderer from '@/lib/Renderer/MermaidRenderer'
import { removeThinkTags } from '@/lib/Parser/ArtifactsParser'

interface MarkdownRendererProps {
  content: string
  className?: string
  style?: React.CSSProperties
}

// 自定义 Markdown 组件配置
const markdownComponents: Components = {
  // @ts-expect-error react-markdown types don't match our usage
  code({ inline, className, children, ...props }) {
    // 内联代码 (单个反引号包裹的内容)
    if (inline) {
      return (
        <span className="text-[#2e3c4f] font-mono bg-[#f1f2f4] px-2 py-1 rounded text-sm" {...props}>
          {children}
        </span>
      );
    }

    // 代码块 (三个反引号包裹的内容)
    const match = /language-(\w+)/.exec(className || '');
    
    // 检查代码块内容，如果只有一个简单的词或短语，可能是误识别的内联代码
    const codeContent = String(children).trim();
    if (codeContent.indexOf('\n') === -1 && codeContent.length < 30 && !match) {
      return (
        <span className="text-[#2e3c4f] font-mono bg-[#f1f2f4] px-2 py-1 rounded text-sm border border-[#e2e8f0]" {...props}>
          {children}
        </span>
      );
    }
    
    if (!match) {
      return (
        <code className="block my-4 bg-[#1e1e1e] text-white p-4 rounded-md overflow-x-auto text-sm font-mono max-w-full break-all" {...props}>
          {children}
        </code>
      );
    }
    
    const language = match[1];
    
    // 检查是否是Mermaid图表
    if (language.toLowerCase() === 'mermaid') {
      return <MermaidRenderer chart={codeContent} />;
    }
    
    // 检查是否有标题
    let title = '';
    if (language.includes(':')) {
      const parts = language.split(':');
      title = parts[1] || '';
    }
    
    return (
                      <CodeBlockRenderer 
        language={language.split(':')[0]} 
        value={String(children).trim()}
        title={title}
      />
    );
  },
  
  h1(props) {
    return <h1 className="text-2xl font-bold mb-4 text-gray-900" {...props} />;
  },
  h2(props) {
    return <h2 className="text-xl font-semibold mb-3 text-gray-900" {...props} />;
  },
  h3(props) {
    return <h3 className="text-lg font-medium mb-2 text-gray-900" {...props} />;
  },
  h4(props) {
    return <h4 className="text-base font-medium mb-2 text-gray-900" {...props} />;
  },
  h5(props) {
    return <h5 className="text-sm font-medium mb-2 text-gray-900" {...props} />;
  },
  h6(props) {
    return <h6 className="text-xs font-medium mb-2 text-gray-900" {...props} />;
  },
  
  ul(props) {
    return <ul className="list-disc pl-6 mb-4 space-y-1" {...props} />;
  },
  ol(props) {
    return <ol className="list-decimal pl-6 mb-4 space-y-1" {...props} />;
  },
  li(props) {
    return <li className="text-gray-700 leading-relaxed" {...props} />;
  },
  
  blockquote(props) {
    return (
      <blockquote className="border-l-4 border-blue-200 pl-4 italic text-gray-600 my-4 bg-blue-50 py-2 rounded-r" {...props} />
    );
  },
  
  table(props) {
    return (
      <div className="overflow-x-auto my-4 border border-gray-200 rounded-lg max-w-full">
        <table className="min-w-full divide-y divide-gray-200 bg-white" {...props} />
      </div>
    );
  },
  thead(props) {
    return <thead className="bg-gray-50" {...props} />;
  },
  tbody(props) {
    return <tbody className="bg-white divide-y divide-gray-200" {...props} />;
  },
  th(props) {
    return (
      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" {...props} />
    );
  },
  td(props) {
    return (
      <td className="px-6 py-4 text-sm text-gray-600 border-b border-gray-100 whitespace-nowrap break-words max-w-xs overflow-hidden text-ellipsis" {...props} />
    );
  },
  
  a(props) {
    return (
      <a 
        className="text-xs bg-[#f4f4f4] rounded-xl px-3 py-1 text-[#757575] hover:text-white hover:bg-black" 
        target={props.href?.startsWith('http') ? '_blank' : undefined}
        rel={props.href?.startsWith('http') ? 'noopener noreferrer' : undefined}
        {...props} 
      />
    );
  },
  
  p(props) {
    return <p className="mb-3 text-gray-700 leading-relaxed break-words overflow-hidden" {...props} />;
  },
  
  strong(props) {
    return <strong className="font-semibold text-gray-900" {...props} />;
  },
  em(props) {
    return <em className="italic text-gray-700" {...props} />;
  },
  
  hr(props) {
    return <hr className="my-6 border-gray-200" {...props} />;
  },
  
  del(props) {
    return <del className="line-through text-gray-500" {...props} />;
  },
  
  // 添加对任务列表的支持
  input(props) {
    if (props.type === 'checkbox') {
      return (
        <input 
          className="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
          {...props} 
        />
      );
    }
    return <input {...props} />;
  },
};

// Markdown 容器的默认样式
const markdownContainerStyles = {
  className: "markdown-body text-gray-800 break-words overflow-hidden",
  style: { 
    fontFamily: 'ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
    wordWrap: 'break-word' as const,
    overflowWrap: 'break-word' as const,
    maxWidth: '100%'
  } as React.CSSProperties
};

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ 
  content, 
  className,
  style 
}) => {
  // 预处理：移除 <think> 标签，避免干扰React渲染
  const cleanContent = removeThinkTags(content)

  return (
    <div 
      className={className || markdownContainerStyles.className}
      style={style || markdownContainerStyles.style}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
        components={markdownComponents}
      >
        {cleanContent}
      </ReactMarkdown>
    </div>
  )
}

export default MarkdownRenderer 