version: '3.8'

services:
  zenus:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: zenus
    ports:
      - "3000:3000"    # Next.js 前端
      - "8000:8000"    # FastAPI 后端
    environment:
      - NODE_ENV=production
      - PYTHONPATH=/app/backend
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    volumes:
      # 如果需要持久化配置文件
      - ./backend/config:/app/backend/config:ro
      # 如果需要持久化日志
      - zenus-logs:/app/logs
    networks:
      - zenus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000", "&&", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  zenus-logs:
    driver: local

networks:
  zenus-network:
    driver: bridge 