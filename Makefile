.PHONY: help dev-frontend dev-backend dev prod install-frontend install-backend install build-frontend docker-build docker-up docker-down clean

# 默认目标
help:
	@echo "MCP AutoGen Chat Application - Available Commands:"
	@echo ""
	@echo "Development:"
	@echo "  dev-frontend    - Start frontend development server"
	@echo "  dev-backend     - Start backend development server"
	@echo "  dev             - Start both frontend and backend in development mode"
	@echo ""
	@echo "Production:"
	@echo "  prod            - Start both services in production mode"
	@echo "  build-frontend  - Build frontend for production"
	@echo ""
	@echo "Installation:"
	@echo "  install         - Install all dependencies"
	@echo "  install-frontend - Install frontend dependencies"
	@echo "  install-backend - Install backend dependencies"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build    - Build Docker image"
	@echo "  docker-up       - Start services with Docker Compose"
	@echo "  docker-down     - Stop Docker services"
	@echo ""
	@echo "Utilities:"
	@echo "  clean           - Clean build artifacts and cache"

# 开发环境
dev-frontend:
	@echo "Starting frontend development server..."
	@cd frontend && npm run dev

dev-backend:
	@echo "Starting backend development server..."
	@cd backend && uvicorn main:app --host 0.0.0.0 --port 8000 --reload

dev:
	@echo "Starting development servers..."
	@echo "Frontend will be available at http://localhost:3000"
	@echo "Backend will be available at http://localhost:8000"
	@make -j2 dev-frontend dev-backend

# 生产环境
build-frontend:
	@echo "Building frontend for production..."
	@cd frontend && npm run build

prod: build-frontend
	@echo "Starting production servers..."
	@echo "Frontend will be available at http://localhost:3000"
	@echo "Backend will be available at http://localhost:8000"
	@cd frontend && npm start -- -p 3000 &
	@cd backend && conda run -n myenv uvicorn main:app --host 0.0.0.0 --port 8000


# 安装依赖
install-frontend:
	@echo "Installing frontend dependencies..."
	@cd frontend && npm install

install-backend:
	@echo "Installing backend dependencies..."
	@cd backend && pip install -r requirements.txt

install: install-frontend install-backend
	@echo "All dependencies installed successfully!"

# Docker 命令
docker-build:
	@echo "Building Docker image..."
	@docker-compose build

docker-up:
	@echo "Starting services with Docker Compose..."
	@docker-compose up -d
	@echo "Services started!"
	@echo "Frontend: http://localhost:3000"
	@echo "Backend: http://localhost:8000"

docker-down:
	@echo "Stopping Docker services..."
	@docker-compose down

docker-logs:
	@echo "Showing Docker logs..."
	@docker-compose logs -f

# 清理
clean:
	@echo "Cleaning build artifacts and cache..."
	@cd frontend && rm -rf .next out node_modules/.cache
	@cd backend && find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	@cd backend && find . -name "*.pyc" -delete 2>/dev/null || true
	@echo "Cleanup completed!"

# 测试 (如果有测试的话)
test-frontend:
	@echo "Running frontend tests..."
	@cd frontend && npm test

test-backend:
	@echo "Running backend tests..."
	@cd backend && python -m pytest

test: test-frontend test-backend

# 代码格式化 (可选)
format-frontend:
	@echo "Formatting frontend code..."
	@cd frontend && npm run lint --fix || echo "Linting completed"

format-backend:
	@echo "Formatting backend code..."
	@cd backend && black . || echo "Please install black: pip install black"

format: format-frontend format-backend 