import asyncio
from agent.llm_service import Model<PERSON>anager
from autogen_agentchat.messages import UserMessage

from autogen_agentchat.agents import AssistantAgent
# from autogen_agentchat.messages import TextMessage

# 初始化模型管理器
manager = ModelManager()

async def main() -> None:

    # model_client = manager.create_client("gpt-4.1")
    # model_client = manager.create_client("gemini-2.5-pro-preview-05-06")
    # model_client = manager.create_client("claude-sonnet-4-20250514")
    model_client = manager.create_client("deepseek-r1-0528")

    # 创建 agent 实例
    agent = AssistantAgent(
        name="assistant_base",
        model_client=model_client,
        system_message="你是一个AI助手，可以回答各种问题并进行对话。",
        reflect_on_tool_use=False,
        model_client_stream=True,  # 是否流式返回内容
    )

    async for message in agent.run_stream(task="介绍一下中国的历史。"):
        print("🧠 [流式输出]:", message)

asyncio.run(main())

# cd backend && python -m playground.test_llm_service