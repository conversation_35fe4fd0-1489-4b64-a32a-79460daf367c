# ────────────────────────────────────────────────────────────────
# Multi‑stage Dockerfile  ‑  MCP AutoGen Chat Application
#   • Stage 1  ➜  Build & prune Next.js frontend (Node 18‑alpine)
#   • Stage 2  ➜  Run FastAPI backend + Miniconda + Next.js + Makefile
# ────────────────────────────────────────────────────────────────

###############################
# 1️⃣  Build   (Node 18)      #
###############################
# Build & keep dev deps
FROM node:18-alpine AS frontend-builder
WORKDIR /app/frontend

COPY frontend/package.json frontend/package-lock.json ./
RUN npm ci --legacy-peer-deps

RUN rm -rf /app/frontend/.next

# 必须复制 tsconfig.json / jsconfig.json 路由别名配置
COPY frontend/ ./

# 保留 devDependencies 用于构建
RUN npm run build

#######################################
# 2️⃣  Runtime  (Python + Node + Conda) #
#######################################
FROM python:3.11 AS backend
ENV DEBIAN_FRONTEND=noninteractive

# ---- System deps + Node.js ----
RUN apt-get update \
  && apt-get install -y --no-install-recommends curl ca-certificates gnupg bash make \
  && curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
  && apt-get install -y --no-install-recommends nodejs \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

# ---- Install Miniconda ----
ENV CONDA_DIR=/opt/conda
ENV PATH=$CONDA_DIR/bin:$PATH

# RUN curl -fsSL https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -o miniconda.sh && \
#     bash miniconda.sh -b -p $CONDA_DIR && \
#     rm miniconda.sh && \
#     conda clean -afy

RUN wget -nv -O miniconda.sh \
        https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh && \
    bash miniconda.sh -b -p $CONDA_DIR && \
    rm -f miniconda.sh && \
    conda clean -afy

# ---- Set default shell to bash to support conda ----
SHELL ["/bin/bash", "-c"]

# ---- Workdir layout ----
WORKDIR /app

#############################
# 2‑a Copy frontend + Make  #
#############################
COPY --from=frontend-builder /app/frontend ./frontend
COPY Makefile ./

#############################
# 2‑b Copy backend & install #
#############################
COPY backend/ /app/backend/
WORKDIR /app/backend
RUN conda create -n myenv python=3.11 pip -y && \
    conda run -n myenv pip install --no-cache-dir -r requirements.txt

########################
# 3️⃣  Entrypoint      #
########################
WORKDIR /app

# ---- Environment ----
ENV NODE_ENV=production \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PORT=8000 \
    NEXT_PORT=3000

# ---- Use Makefile as entrypoint ----
ENTRYPOINT ["make", "prod"]

EXPOSE 3000 8000
