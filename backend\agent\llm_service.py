from typing import List, Dict, Optional
import yaml
from config import ModelConfig
from pprint import pprint

# Clients
from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_ext.models.anthropic import AnthropicChatCompletionClient


class ModelManager:
    """统一的模型管理器"""
    def __init__(self, config_path: str = "model_config.yaml"):
        self.models: Dict[str, ModelConfig] = {}
        self.load_config(config_path)
    
    def load_config(self, config_path: str):
        """初始化加载模型配置"""
        try:
            # Load model config from yaml file
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) # json
            
            # 解析各个提供商的模型配置
            for provider, provider_config in config.items():
                if not isinstance(provider_config, dict):
                    continue
                
                # 获取提供商的基础配置
                api_key = provider_config.get("api_key", "")
                base_url = provider_config.get("base_url")
                models = provider_config.get("models", [])
                
                # 为每个模型创建配置
                for model_info in models:
                    model_name = model_info.get("model_name", "")
                    if not model_name:
                        continue

                    self.models[model_name] = ModelConfig(
                        provider=provider,
                        model_name=model_name,
                        model_display_name=model_info.get("model_display_name", model_name),
                        model_version=model_info.get("model_version"),
                        api_key=api_key,
                        base_url=base_url
                    )
            
            print(f"✅ 成功从 {config_path} 加载 {len(self.models)} 个模型配置")
            
        except Exception as e:
            print(f"❌ 加载模型配置失败: {e}")
            print("请检查 model.yaml 配置文件是否存在且格式正确")
            raise
    
    def get_available_models(self) -> List[Dict[str, str]]:
        """获取可用的模型列表"""
        return [
            {
                "id": config.model_name,  # 前端期望的id字段
                "name": config.model_display_name,  # 前端期望的name字段
                "display_name": config.model_display_name,  # 前端期望的display_name字段
                "provider": config.provider  # 前端期望的provider字段
            }
            for model_name, config in self.models.items()
            if config.api_key  # 只返回有API key的模型
        ]
    
    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """通过 model_name 获取模型配置"""
        print(f"{model_name}的模型配置为{self.models.get(model_name)}")
        return self.models.get(model_name)
    
    def create_client(self, model_name: str):
        """根据 model_name 创建 Autogen 兼容的 LLM 客户端"""
        config = self.get_model_config(model_name)
        pprint(config)
        if not config:
            raise ValueError(f"模型 {model_name} 不存在")
        
        if not config.api_key:
            raise ValueError(f"模型 {model_name} 缺少API密钥")

        match config.provider:
            case "openai":
                return OpenAIChatCompletionClient(
                    api_key=config.api_key,
                    model=config.model_name,
                    base_url=config.base_url or "",
                )
            case "anthropic":
                return AnthropicChatCompletionClient(
                    api_key=config.api_key,
                    model=config.model_name,
                    base_url=config.base_url,
                    max_tokens=20000,
                )
            case "gemini":
                return OpenAIChatCompletionClient(
                    api_key=config.api_key,
                    model=config.model_name,
                    base_url=config.base_url or "",
                    model_info={  # ✅ 手动绕过模型校验
                        "json_output": False,
                        "function_calling": False,
                        "vision": False,
                        "structured_output": False,
                        "family": "gemini",  # 自定义标识即可
                    },
                )
            case "deepseek":
                return OpenAIChatCompletionClient(
                    api_key=config.api_key,
                    model=config.model_name,
                    base_url=config.base_url or "",
                    model_info={  # ✅ 手动绕过模型校验
                        "json_output": False,
                        "function_calling": False,
                        "vision": False,
                        "structured_output": False,
                        "family": "deepseek",  # 自定义标识即可
                    },
                )
            case _:
                raise ValueError(f"不支持的模型提供商: {config.provider}")


if __name__ == "__main__":

    model_manager = ModelManager()
    print(model_manager.get_available_models())

    # python -m agent.llm_service


